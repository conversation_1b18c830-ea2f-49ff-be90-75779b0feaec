import React, { useState, useRef, useEffect, useCallback, useMemo, createContext, useContext, useReducer } from 'react';
import { Upload, Download, Play, Pause, Square, Monitor, Volume2, Settings, SkipForward, SkipBack, Repeat, Save, Folder, Music, Layers, Zap, Palette, RotateCcw, ChevronRight, ChevronLeft, ChevronDown, Grid, Maximize2, X, Plus, Trash2, Copy, Edit2, Lock, Unlock, Eye, EyeOff, Sliders, Activity, Wifi, WifiOff, AlertCircle, CheckCircle } from 'lucide-react';

// ==================== CONTEXT & STATE MANAGEMENT ====================

const AppContext = createContext();

const initialState = {
  project: {
    name: 'Neues Projekt',
    version: '2.0',
    sequences: [],
    sets: [],
    svgContent: '',
    metadata: {
      created: new Date().toISOString(),
      modified: new Date().toISOString(),
      author: 'DJ'
    }
  },
  player: {
    isPlaying: false,
    currentSequence: null,
    currentSet: null,
    crossfadeValue: 50,
    crossfadeMode: false,
    masterVolume: 100,
    deckA: {
      sequence: null,
      isPlaying: false,
      bpm: 120,
      playbackSpeed: 1.0,
      isBpmSynced: false,
      volume: 100,
      beatDetected: false
    },
    deckB: {
      sequence: null,
      isPlaying: false,
      bpm: 120,
      playbackSpeed: 1.0,
      isBpmSynced: false,
      volume: 100,
      beatDetected: false
    }
  },
  editor: {
    selectedElement: null,
    selectedSequence: null,
    showEffectPanel: true,
    gridEnabled: true,
    snapToGrid: true,
    history: [],
    historyIndex: -1
  },
  ui: {
    projectionWindow: null,
    audioContext: null,
    analyser: null,
    activeTab: 'editor',
    notifications: [],
    theme: 'dark',
    sidebarCollapsed: false,
    rightSidebarCollapsed: false,
    sequencesExpanded: true,
    beatSyncExpanded: false
  },
  performance: {
    fps: 60,
    frameTime: 0,
    activeAnimations: 0,
    memoryUsage: 0
  },
  beatEngine: {
    bpm: 120,
    isPlaying: false,
    beatCount: 0,
    barCount: 0,
    isListening: false,
    confidence: 0,
    quantization: 'beat'
  }
};

function appReducer(state, action) {
  switch (action.type) {
    case 'SET_PROJECT':
      return { ...state, project: { ...state.project, ...action.payload } };
    case 'UPDATE_PLAYER':
      return { ...state, player: { ...state.player, ...action.payload } };
    case 'UPDATE_EDITOR':
      return { ...state, editor: { ...state.editor, ...action.payload } };
    case 'UPDATE_UI':
      return { ...state, ui: { ...state.ui, ...action.payload } };
    case 'UPDATE_PERFORMANCE':
      return { ...state, performance: { ...state.performance, ...action.payload } };
    case 'UPDATE_BEAT_ENGINE':
      return { ...state, beatEngine: { ...state.beatEngine, ...action.payload } };
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: [...state.ui.notifications, action.payload]
        }
      };
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        ui: {
          ...state.ui,
          notifications: state.ui.notifications.filter(n => n.id !== action.payload)
        }
      };
    case 'UNDO':
      if (state.editor.historyIndex > 0) {
        const newIndex = state.editor.historyIndex - 1;
        return {
          ...state,
          project: state.editor.history[newIndex],
          editor: { ...state.editor, historyIndex: newIndex }
        };
      }
      return state;
    case 'REDO':
      if (state.editor.historyIndex < state.editor.history.length - 1) {
        const newIndex = state.editor.historyIndex + 1;
        return {
          ...state,
          project: state.editor.history[newIndex],
          editor: { ...state.editor, historyIndex: newIndex }
        };
      }
      return state;
    case 'ADD_TO_HISTORY':
      const newHistory = state.editor.history.slice(0, state.editor.historyIndex + 1);
      newHistory.push(action.payload);
      return {
        ...state,
        editor: {
          ...state.editor,
          history: newHistory.slice(-50),
          historyIndex: Math.min(newHistory.length - 1, 49)
        }
      };
    default:
      return state;
  }
}

// ==================== BEAT ENGINE ====================

class BeatEngine {
  constructor(dispatch) {
    this.dispatch = dispatch;
    this.bpm = 120;
    this.isPlaying = false;
    this.startTime = 0;
    this.beatCount = 0;
    this.barCount = 0;
    this.lastBeatTime = 0;
    this.beatCallbacks = new Map();
    this.quantizeQueue = [];
    
    this.audioContext = null;
    this.analyser = null;
    this.beatHistory = [];
    this.confidenceThreshold = 0.8;
    this.confidence = 0;
    
    this.metronomeEnabled = true;
    this.visualBeatIndicator = null;
  }

  async initAudioAnalysis() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;
      this.analyser.smoothingTimeConstant = 0.3;

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: { 
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false
        } 
      });
      
      const source = this.audioContext.createMediaStreamSource(stream);
      source.connect(this.analyser);
      
      this.startBeatDetection();
      this.dispatch({ type: 'UPDATE_BEAT_ENGINE', payload: { isListening: true } });
      return true;
    } catch (error) {
      console.error('Audio analysis initialization failed:', error);
      return false;
    }
  }

  startBeatDetection() {
    const bufferLength = this.analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const energyHistory = [];
    let lastBeatDetected = 0;
    
    const detectBeats = () => {
      if (!this.analyser) return;
      
      this.analyser.getByteFrequencyData(dataArray);
      
      const bassRange = dataArray.slice(0, Math.floor(bufferLength * 0.1));
      const bassEnergy = bassRange.reduce((sum, val) => sum + val * val, 0) / bassRange.length;
      
      energyHistory.push(bassEnergy);
      if (energyHistory.length > 20) energyHistory.shift();
      
      const avgEnergy = energyHistory.reduce((sum, val) => sum + val) / energyHistory.length;
      const variance = energyHistory.reduce((sum, val) => sum + Math.pow(val - avgEnergy, 2), 0) / energyHistory.length;
      const threshold = avgEnergy + Math.sqrt(variance) * 1.5;
      
      const now = performance.now();
      
      if (bassEnergy > threshold && (now - lastBeatDetected) > 200) {
        const timeSinceLastBeat = now - this.lastBeatTime;
        
        if (timeSinceLastBeat > 150) {
          this.onBeatDetected(now, timeSinceLastBeat);
          lastBeatDetected = now;
        }
      }
      
      requestAnimationFrame(detectBeats);
    };
    
    detectBeats();
  }

  onBeatDetected(timestamp, interval) {
    if (interval > 300 && interval < 2000) {
      const detectedBPM = 60000 / interval;
      
      this.beatHistory.push(detectedBPM);
      if (this.beatHistory.length > 8) this.beatHistory.shift();
      
      if (this.beatHistory.length >= 4) {
        const avgBPM = this.beatHistory.reduce((sum, bpm) => sum + bpm) / this.beatHistory.length;
        const variance = this.beatHistory.reduce((sum, bpm) => sum + Math.pow(bpm - avgBPM, 2), 0) / this.beatHistory.length;
        this.confidence = Math.max(0, 1 - variance / 400);
        
        if (this.confidence > this.confidenceThreshold) {
          this.setBPM(Math.round(avgBPM));
        }
        
        this.dispatch({ type: 'UPDATE_BEAT_ENGINE', payload: { confidence: this.confidence } });
      }
    }
    
    this.lastBeatTime = timestamp;
    this.beatCount++;
    
    if (this.beatCount % 4 === 0) {
      this.barCount++;
      this.triggerBarCallbacks();
    }
    
    this.triggerBeatCallbacks();
    this.processQuantizeQueue();
    this.updateVisualBeatIndicator();
    
    this.dispatch({ 
      type: 'UPDATE_BEAT_ENGINE', 
      payload: { 
        beatCount: this.beatCount,
        barCount: this.barCount 
      } 
    });
  }

  setBPM(newBPM) {
    this.bpm = Math.max(30, Math.min(300, newBPM));
    this.beatInterval = 60000 / this.bpm;
    
    this.startTime = performance.now();
    this.beatCount = 0;
    this.barCount = 0;
    
    this.dispatch({ type: 'UPDATE_BEAT_ENGINE', payload: { bpm: this.bpm } });
    this.broadcastBPMChange();
  }

  tapBeat() {
    const now = performance.now();
    const interval = now - this.lastBeatTime;
    
    if (interval > 200 && interval < 3000) {
      this.onBeatDetected(now, interval);
    } else {
      this.lastBeatTime = now;
      this.startTime = now;
      this.beatCount = 0;
      this.barCount = 0;
    }
  }

  getBeatPhase() {
    if (!this.isPlaying) return 0;
    
    const elapsed = performance.now() - this.lastBeatTime;
    const beatInterval = 60000 / this.bpm;
    return (elapsed % beatInterval) / beatInterval;
  }

  getBarPhase() {
    const beatInBar = this.beatCount % 4;
    const beatPhase = this.getBeatPhase();
    return (beatInBar + beatPhase) / 4;
  }

  quantizeToGrid(callback, quantization = 'beat') {
    const entry = {
      callback,
      quantization,
      timestamp: performance.now()
    };
    
    if (quantization === 'immediate') {
      callback();
      return;
    }
    
    this.quantizeQueue.push(entry);
  }

  processQuantizeQueue() {
    this.quantizeQueue = this.quantizeQueue.filter(entry => {
      const shouldTrigger = 
        (entry.quantization === 'beat') ||
        (entry.quantization === 'bar' && this.beatCount % 4 === 0) ||
        (entry.quantization === '2-bar' && this.beatCount % 8 === 0);
      
      if (shouldTrigger) {
        entry.callback();
        return false;
      }
      
      const age = performance.now() - entry.timestamp;
      return age < (4 * 4 * this.beatInterval);
    });
  }

  onBeat(id, callback) {
    this.beatCallbacks.set(id, { type: 'beat', callback });
  }

  onBar(id, callback) {
    this.beatCallbacks.set(id, { type: 'bar', callback });
  }

  triggerBeatCallbacks() {
    this.beatCallbacks.forEach(({ type, callback }) => {
      if (type === 'beat') callback(this.beatCount, this.getBeatPhase());
    });
  }

  triggerBarCallbacks() {
    this.beatCallbacks.forEach(({ type, callback }) => {
      if (type === 'bar') callback(this.barCount, this.getBarPhase());
    });
  }

  updateVisualBeatIndicator() {
    if (this.visualBeatIndicator && this.metronomeEnabled) {
      this.visualBeatIndicator.classList.add('beat-flash');
      setTimeout(() => {
        this.visualBeatIndicator?.classList.remove('beat-flash');
      }, 100);
    }

    this.broadcastBeat();
  }

  broadcastBeat() {
    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'beatUpdate',
        beatCount: this.beatCount,
        barCount: this.barCount,
        bpm: this.bpm,
        beatPhase: this.getBeatPhase(),
        barPhase: this.getBarPhase()
      }, '*');
    }
  }

  broadcastBPMChange() {
    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'bpmChange',
        bpm: this.bpm,
        beatInterval: this.beatInterval
      }, '*');
    }
  }

  stopListening() {
    this.dispatch({ type: 'UPDATE_BEAT_ENGINE', payload: { isListening: false } });
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
      this.analyser = null;
    }
  }

  getConfidence() {
    return this.confidence;
  }
}

// ==================== GROUP EFFECTS ENGINE ====================

class GroupEffectsEngine {
  constructor(svgContent) {
    this.groups = this.analyzeGroups(svgContent);
    this.activeGroupEffects = new Map();
    this.groupPatterns = new Map();
  }

  analyzeGroups(svgContent) {
    // Return empty groups if no SVG content
    if (!svgContent || svgContent.trim() === '') {
      return {
        balken: [], gefache: [], fenster: [], all: [],
        struktur: [], obergeschoss: [], erdgeschoss: [],
        horizontal: [], vertical: [], linkeSeite: [], rechteSeite: [],
        lichtquellen: [], flaechen: [],
        obereFenster: [], untereFenster: [], linkeFenster: [], rechteFenster: [],
        kleineFenster: [], grosseFenster: [],
        obergeschossFenster: [], erdgeschossFenster: []
      };
    }

    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');

    // Check for parsing errors
    const parserError = svgDoc.querySelector('parsererror');
    if (parserError) {
      console.warn('SVG parsing error:', parserError.textContent);
      return {
        balken: [], gefache: [], fenster: [], all: [],
        struktur: [], obergeschoss: [], erdgeschoss: [],
        horizontal: [], vertical: [], linkeSeite: [], rechteSeite: [],
        lichtquellen: [], flaechen: [],
        obereFenster: [], untereFenster: [], linkeFenster: [], rechteFenster: [],
        kleineFenster: [], grosseFenster: [],
        obergeschossFenster: [], erdgeschossFenster: []
      };
    }

    const elements = svgDoc.querySelectorAll('[id]');

    const groups = {
      balken: [],
      gefache: [],
      fenster: [],
      all: [],

      struktur: [],
      obergeschoss: [],
      erdgeschoss: [],

      horizontal: [],
      vertical: [],
      linkeSeite: [],
      rechteSeite: [],

      lichtquellen: [],
      flaechen: [],

      obereFenster: [],
      untereFenster: [],
      linkeFenster: [],
      rechteFenster: [],
      kleineFenster: [],
      grosseFenster: [],

      obergeschossFenster: [],
      erdgeschossFenster: []
    };

    elements.forEach(element => {
      const id = element.id;

      // Check if element has getBBox method (SVG elements)
      if (typeof element.getBBox !== 'function') {
        console.warn(`Element ${id} does not support getBBox(), skipping...`);
        return;
      }

      let bbox;
      try {
        bbox = element.getBBox();
      } catch (error) {
        console.warn(`Failed to get bounding box for element ${id}:`, error);
        return;
      }

      const centerX = bbox.x + bbox.width / 2;
      const centerY = bbox.y + bbox.height / 2;

      const elementData = { id, element, bbox, centerX, centerY };
      
      if (id.startsWith('B')) {
        groups.balken.push(elementData);
        groups.struktur.push(elementData);
      } else if (id.startsWith('G')) {
        groups.gefache.push(elementData);
        groups.flaechen.push(elementData);
      } else if (id.startsWith('F')) {
        groups.fenster.push(elementData);
        groups.lichtquellen.push(elementData);
        
        if (centerY < 250) {
          groups.obereFenster.push(elementData);
          groups.obergeschossFenster.push(elementData);
        } else {
          groups.untereFenster.push(elementData);
          groups.erdgeschossFenster.push(elementData);
        }
        
        if (centerX < 400) {
          groups.linkeFenster.push(elementData);
        } else {
          groups.rechteFenster.push(elementData);
        }
        
        if (bbox.width <= 60) {
          groups.kleineFenster.push(elementData);
        } else {
          groups.grosseFenster.push(elementData);
        }
      }
      
      groups.all.push(elementData);
      
      if (centerY < 250) {
        groups.obergeschoss.push(elementData);
      } else {
        groups.erdgeschoss.push(elementData);
      }
      
      if (centerX < 400) {
        groups.linkeSeite.push(elementData);
      } else {
        groups.rechteSeite.push(elementData);
      }
      
      if (bbox.width > bbox.height * 1.5) {
        groups.horizontal.push(elementData);
      }
      if (bbox.height > bbox.width * 1.5) {
        groups.vertical.push(elementData);
      }
    });

    return groups;
  }

  getGroupPatterns() {
    return {
      sequence: {
        name: 'Sequenziell',
        description: 'Aktiviert Elemente nacheinander',
        apply: (elements, timing) => this.applySequential(elements, timing)
      },
      
      alternating: {
        name: 'Abwechselnd',
        description: 'Abwechselnde Aktivierung (every other)',
        apply: (elements, timing) => this.applyAlternating(elements, timing)
      },
      
      wave: {
        name: 'Welle',
        description: 'Wellenförmige Ausbreitung',
        apply: (elements, timing) => this.applyWave(elements, timing)
      },
      
      spiral: {
        name: 'Spiral',
        description: 'Spiralförmige Aktivierung vom Zentrum',
        apply: (elements, timing) => this.applySpiral(elements, timing)
      },
      
      random: {
        name: 'Zufall',
        description: 'Zufällige Aktivierung',
        apply: (elements, timing) => this.applyRandom(elements, timing)
      },
      
      simultaneous: {
        name: 'Gleichzeitig',
        description: 'Alle gleichzeitig',
        apply: (elements, timing) => this.applySimultaneous(elements, timing)
      },
      
      pairs: {
        name: 'Paare',
        description: 'Aktivierung in Paaren',
        apply: (elements, timing) => this.applyPairs(elements, timing)
      },
      
      mirror: {
        name: 'Spiegelung',
        description: 'Gespiegelte Aktivierung',
        apply: (elements, timing) => this.applyMirror(elements, timing)
      },
      
      stockwerkWave: {
        name: 'Stockwerk Welle',
        description: 'Welle läuft von unten nach oben durch die Stockwerke',
        apply: (elements, timing) => this.applyStockwerkWave(elements, timing)
      },
      
      alternatingStockwerk: {
        name: 'Abwechselnde Stockwerke',
        description: 'Obergeschoss und Erdgeschoss abwechselnd',
        apply: (elements, timing) => this.applyAlternatingStockwerk(elements, timing)
      },
      
      fensterReihe: {
        name: 'Fenster-Reihe',
        description: 'Von links nach rechts durch alle Fenster',
        apply: (elements, timing) => this.applyFensterReihe(elements, timing)
      },
      
      architekturalWave: {
        name: 'Architektonische Welle',
        description: 'Struktur → Flächen → Fenster',
        apply: (elements, timing) => this.applyArchitekturalWave(elements, timing)
      },

      sequentialChase: {
        name: 'Sequential Chase',
        description: 'Einer nach dem anderen, vorheriger stoppt',
        apply: (elements, timing) => this.applySequentialChase(elements, timing)
      },

      alternatingChase: {
        name: 'Alternating Chase',
        description: 'Abwechselnd, vorheriger stoppt',
        apply: (elements, timing) => this.applyAlternatingChase(elements, timing)
      },

      waveChase: {
        name: 'Wave Chase',
        description: 'Welle mit Stop-Effekt',
        apply: (elements, timing) => this.applyWaveChase(elements, timing)
      }
    };
  }

  applySequential(elements, timing) {
    const intervals = [];
    const interval = timing.duration / elements.length;
    
    elements.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: index * interval,
        duration: timing.elementDuration || interval * 2
      });
    });
    
    return intervals;
  }

  applyAlternating(elements, timing) {
    const intervals = [];
    const groupA = elements.filter((_, index) => index % 2 === 0);
    const groupB = elements.filter((_, index) => index % 2 === 1);
    
    groupA.forEach(element => {
      intervals.push({
        elementId: element.id,
        startTime: 0,
        duration: timing.elementDuration || timing.duration / 2,
        beatOffset: 0
      });
    });
    
    groupB.forEach(element => {
      intervals.push({
        elementId: element.id,
        startTime: timing.duration / 2,
        duration: timing.elementDuration || timing.duration / 2,
        beatOffset: 2
      });
    });
    
    return intervals;
  }

  applyWave(elements, timing) {
    const intervals = [];
    
    const sortedElements = [...elements].sort((a, b) => {
      const centerA = a.bbox.x + a.bbox.width / 2;
      const centerB = b.bbox.x + b.bbox.width / 2;
      return centerA - centerB;
    });
    
    const waveLength = timing.duration;
    const elementSpacing = waveLength / (sortedElements.length + 1);
    
    sortedElements.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: index * elementSpacing,
        duration: timing.elementDuration || elementSpacing * 3,
        wavePosition: index
      });
    });
    
    return intervals;
  }

  applySpiral(elements, timing) {
    const intervals = [];
    
    const avgX = elements.reduce((sum, el) => sum + el.bbox.x + el.bbox.width / 2, 0) / elements.length;
    const avgY = elements.reduce((sum, el) => sum + el.bbox.y + el.bbox.height / 2, 0) / elements.length;
    
    const sortedElements = [...elements].sort((a, b) => {
      const centerA = { x: a.bbox.x + a.bbox.width / 2, y: a.bbox.y + a.bbox.height / 2 };
      const centerB = { x: b.bbox.x + b.bbox.width / 2, y: b.bbox.y + b.bbox.height / 2 };
      
      const distA = Math.sqrt(Math.pow(centerA.x - avgX, 2) + Math.pow(centerA.y - avgY, 2));
      const distB = Math.sqrt(Math.pow(centerB.x - avgX, 2) + Math.pow(centerB.y - avgY, 2));
      
      const angleA = Math.atan2(centerA.y - avgY, centerA.x - avgX);
      const angleB = Math.atan2(centerB.y - avgY, centerB.x - avgX);
      
      return (distA + angleA) - (distB + angleB);
    });
    
    const spiralDuration = timing.duration;
    const elementSpacing = spiralDuration / sortedElements.length;
    
    sortedElements.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: index * elementSpacing,
        duration: timing.elementDuration || elementSpacing * 2,
        spiralIndex: index
      });
    });
    
    return intervals;
  }

  applyRandom(elements, timing) {
    const intervals = [];
    const shuffled = [...elements].sort(() => Math.random() - 0.5);
    const interval = timing.duration / shuffled.length;
    
    shuffled.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: index * interval + Math.random() * interval * 0.5,
        duration: timing.elementDuration || interval * 1.5
      });
    });
    
    return intervals;
  }

  applySimultaneous(elements, timing) {
    const intervals = [];
    
    elements.forEach(element => {
      intervals.push({
        elementId: element.id,
        startTime: 0,
        duration: timing.elementDuration || timing.duration
      });
    });
    
    return intervals;
  }

  applyPairs(elements, timing) {
    const intervals = [];
    const pairDuration = timing.duration / Math.ceil(elements.length / 2);
    
    for (let i = 0; i < elements.length; i += 2) {
      const pairIndex = Math.floor(i / 2);
      const startTime = pairIndex * pairDuration;
      
      intervals.push({
        elementId: elements[i].id,
        startTime,
        duration: timing.elementDuration || pairDuration
      });
      
      if (i + 1 < elements.length) {
        intervals.push({
          elementId: elements[i + 1].id,
          startTime,
          duration: timing.elementDuration || pairDuration
        });
      }
    }
    
    return intervals;
  }

  applyMirror(elements, timing) {
    const intervals = [];
    const centerX = 400; // Mitte des SVG
    
    const leftElements = elements.filter(el => el.centerX < centerX);
    const rightElements = elements.filter(el => el.centerX >= centerX);
    
    const maxLength = Math.max(leftElements.length, rightElements.length);
    const interval = timing.duration / maxLength;
    
    for (let i = 0; i < maxLength; i++) {
      const startTime = i * interval;
      
      if (i < leftElements.length) {
        intervals.push({
          elementId: leftElements[i].id,
          startTime,
          duration: timing.elementDuration || interval * 2
        });
      }
      
      if (i < rightElements.length) {
        intervals.push({
          elementId: rightElements[i].id,
          startTime,
          duration: timing.elementDuration || interval * 2
        });
      }
    }
    
    return intervals;
  }

  applyStockwerkWave(elements, timing) {
    const intervals = [];
    
    const erdgeschoss = elements.filter(el => el.centerY >= 250);
    const obergeschoss = elements.filter(el => el.centerY < 250);
    
    const stockwerkDuration = timing.duration / 2;
    
    erdgeschoss.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: (index / erdgeschoss.length) * stockwerkDuration,
        duration: timing.elementDuration,
        stockwerk: 'erdgeschoss'
      });
    });
    
    obergeschoss.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: stockwerkDuration + (index / obergeschoss.length) * stockwerkDuration,
        duration: timing.elementDuration,
        stockwerk: 'obergeschoss'
      });
    });
    
    return intervals;
  }

  applyAlternatingStockwerk(elements, timing) {
    const intervals = [];
    const erdgeschoss = elements.filter(el => el.centerY >= 250);
    const obergeschoss = elements.filter(el => el.centerY < 250);
    
    erdgeschoss.forEach(element => {
      intervals.push({
        elementId: element.id,
        startTime: 0,
        duration: timing.elementDuration,
        beatOffset: 0,
        group: 'erdgeschoss'
      });
    });
    
    obergeschoss.forEach(element => {
      intervals.push({
        elementId: element.id,
        startTime: timing.duration / 2,
        duration: timing.elementDuration,
        beatOffset: 2,
        group: 'obergeschoss'
      });
    });
    
    return intervals;
  }

  applyFensterReihe(elements, timing) {
    const intervals = [];
    
    const fenster = elements.filter(el => el.id.startsWith('F'));
    const sortedFenster = fenster.sort((a, b) => a.centerX - b.centerX);
    
    const fensterInterval = timing.duration / sortedFenster.length;
    
    sortedFenster.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: index * fensterInterval,
        duration: timing.elementDuration,
        fensterIndex: index
      });
    });
    
    return intervals;
  }

  applyArchitekturalWave(elements, timing) {
    const intervals = [];
    
    const balken = elements.filter(el => el.id.startsWith('B'));
    const gefache = elements.filter(el => el.id.startsWith('G'));
    const fenster = elements.filter(el => el.id.startsWith('F'));
    
    const phaseDuration = timing.duration / 3;
    
    balken.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: (index / balken.length) * phaseDuration,
        duration: timing.elementDuration,
        phase: 'struktur'
      });
    });
    
    gefache.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: phaseDuration + (index / gefache.length) * phaseDuration,
        duration: timing.elementDuration,
        phase: 'flaechen'
      });
    });
    
    fenster.forEach((element, index) => {
      intervals.push({
        elementId: element.id,
        startTime: 2 * phaseDuration + (index / fenster.length) * phaseDuration,
        duration: timing.elementDuration,
        phase: 'licht'
      });
    });
    
    return intervals;
  }

  // Chase patterns - where each new effect stops the previous one
  applySequentialChase(elements, timing) {
    const intervals = [];
    const elementDuration = timing.elementDuration || 1000;
    const gapBetweenElements = elementDuration * 0.5; // Faster transitions for better chase effect

    // Create multiple cycles for endless loop
    const totalCycleDuration = elements.length * gapBetweenElements;
    const cycles = Math.max(5, Math.ceil(timing.duration / totalCycleDuration));

    for (let cycle = 0; cycle < cycles; cycle++) {
      elements.forEach((element, index) => {
        const globalIndex = cycle * elements.length + index;
        intervals.push({
          elementId: element.id,
          startTime: globalIndex * gapBetweenElements,
          duration: elementDuration,
          stopPrevious: true,
          chaseIndex: index, // Use element index for proper cycling
          chaseGroup: `sequentialChase_${timing.duration}`, // Unique group per effect instance
          cycle: cycle,
          elementIndex: index,
          isChaseLoop: true
        });
      });
    }

    return intervals;
  }

  applyAlternatingChase(elements, timing) {
    const intervals = [];
    const groupA = elements.filter((_, index) => index % 2 === 0);
    const groupB = elements.filter((_, index) => index % 2 === 1);
    const elementDuration = timing.elementDuration || 1000;
    const gapBetweenElements = elementDuration * 0.5;

    // Calculate cycles for endless loop
    const totalElements = groupA.length + groupB.length;
    const totalCycleDuration = totalElements * gapBetweenElements;
    const cycles = Math.max(5, Math.ceil(timing.duration / totalCycleDuration));

    for (let cycle = 0; cycle < cycles; cycle++) {
      const cycleOffset = cycle * totalCycleDuration;

      // Group A in this cycle
      groupA.forEach((element, index) => {
        intervals.push({
          elementId: element.id,
          startTime: cycleOffset + index * gapBetweenElements * 2,
          duration: elementDuration,
          stopPrevious: true,
          chaseIndex: index * 2, // Use element position for proper cycling
          chaseGroup: `alternatingChase_${timing.duration}`,
          cycle: cycle,
          group: 'A',
          isChaseLoop: true
        });
      });

      // Group B in this cycle (offset by one slot)
      groupB.forEach((element, index) => {
        intervals.push({
          elementId: element.id,
          startTime: cycleOffset + (index * 2 + 1) * gapBetweenElements,
          duration: elementDuration,
          stopPrevious: true,
          chaseIndex: index * 2 + 1, // Use element position for proper cycling
          chaseGroup: `alternatingChase_${timing.duration}`,
          cycle: cycle,
          group: 'B',
          isChaseLoop: true
        });
      });
    }

    return intervals.sort((a, b) => a.startTime - b.startTime);
  }

  applyWaveChase(elements, timing) {
    const intervals = [];
    const elementDuration = timing.elementDuration || 1000;
    const waveSpeed = elementDuration * 0.5;

    // Calculate cycles for endless loop
    const totalCycleDuration = elements.length * waveSpeed;
    const cycles = Math.max(5, Math.ceil(timing.duration / totalCycleDuration));

    for (let cycle = 0; cycle < cycles; cycle++) {
      elements.forEach((element, index) => {
        // Create wave pattern that repeats
        const wavePhase = (index / elements.length) * Math.PI * 2;
        const normalizedDelay = (Math.sin(wavePhase) + 1) * 0.5; // 0 to 1
        const cycleOffset = cycle * totalCycleDuration;
        const delay = cycleOffset + normalizedDelay * totalCycleDuration;

        intervals.push({
          elementId: element.id,
          startTime: delay,
          duration: elementDuration,
          stopPrevious: true,
          chaseIndex: index, // Use element index for proper cycling
          chaseGroup: `waveChase_${timing.duration}`,
          cycle: cycle,
          wavePhase: normalizedDelay,
          isChaseLoop: true
        });
      });
    }

    return intervals.sort((a, b) => a.startTime - b.startTime);
  }

  getGroupEffectLibrary() {
    return {
      balkenGlow: {
        name: '🔥 Balken Glühen - Intensiv',
        targetGroup: 'balken',
        pattern: 'simultaneous',
        effect: {
          type: 'pulse',
          parameters: {
            color: '#ff8844',
            intensity: 1.2,
            speed: 1.8,
            pulseWidth: 0.7,
            brightness: 1.4
          }
        },
        timing: {
          duration: 4000,
          elementDuration: 2000,
          beatSync: true,
          quantization: 'bar'
        }
      },

      balkenShadowCast: {
        name: '🌑 Balken Schattenwurf - Dynamisch',
        targetGroup: 'balken',
        pattern: 'sequence',
        effect: {
          type: 'shadowCast',
          parameters: {
            shadowLength: 75,
            shadowAngle: 45,
            shadowOpacity: 0.8,
            rotation: 720,
            shadowColor: '#2c1810'
          }
        },
        timing: {
          duration: 8000,
          elementDuration: 2000,
          beatSync: true,
          quantization: 'beat'
        }
      },

      gefacheRainbow: {
        name: '🌈 Gefache Regenbogen - Lebendig',
        targetGroup: 'gefache',
        pattern: 'alternating',
        effect: {
          type: 'rainbow',
          parameters: {
            speed: 2.5,
            intensity: 1.1,
            cycles: 3,
            saturation: 95,
            lightness: 60
          }
        },
        timing: {
          duration: 6000,
          elementDuration: 1500,
          beatSync: true,
          quantization: 'beat'
        }
      },

      fensterGlow: {
        name: '✨ Fenster Glühen - Warm',
        targetGroup: 'fenster',
        pattern: 'simultaneous',
        effect: {
          type: 'windowGlow',
          parameters: {
            glowColor: '#FFE4B5',
            intensity: 1.3,
            spreadRadius: 15,
            innerGlow: true,
            pulseRate: 0.8
          }
        },
        timing: {
          duration: 5000,
          elementDuration: 2500,
          beatSync: true,
          quantization: 'bar'
        }
      },

      gefacheAlternating: {
        name: '🎨 Gefache Farbverlauf - Sanft',
        targetGroup: 'gefache',
        pattern: 'alternating',
        effect: {
          type: 'colorFade',
          parameters: {
            from: '#F5E6D3',
            to: '#ff6b35',
            speed: 1.5,
            easing: 'easeInOut'
          }
        },
        timing: {
          duration: 3000,
          elementDuration: 1500,
          beatSync: true,
          quantization: 'beat'
        }
      },

      gefacheBlink: {
        name: '⚡ Gefache Stroboskop - Rhythmisch',
        targetGroup: 'gefache',
        pattern: 'simultaneous',
        effect: {
          type: 'strobe',
          parameters: {
            color: '#ffffff',
            frequency: 12,
            intensity: 1.4,
            dutyCycle: 0.3
          }
        },
        timing: {
          duration: 2000,
          elementDuration: 1000,
          beatSync: true,
          quantization: 'beat'
        }
      },

      architecturalShowcase: {
        name: '🏛️ Architektur Showcase - Elegant',
        targetGroup: 'all',
        pattern: 'simultaneous',
        effect: {
          type: 'architecturalGlow',
          parameters: {
            balkenColor: '#8B4513',
            gefachColor: '#F5E6D3',
            fensterColor: '#FFE4B5',
            intensity: 1.0,
            pulseRate: 0.6
          }
        },
        timing: {
          duration: 8000,
          elementDuration: 4000,
          beatSync: true,
          quantization: 'bar'
        }
      },

      breathingHouse: {
        name: '🫁 Haus Atmung - Beruhigend',
        targetGroup: 'all',
        pattern: 'simultaneous',
        effect: {
          type: 'breathe',
          parameters: {
            breathRate: 0.15,
            depth: 0.4,
            asymmetry: 0.6,
            minOpacity: 0.4
          }
        },
        timing: {
          duration: 12000,
          elementDuration: 6000,
          beatSync: false,
          quantization: 'immediate'
        }
      },

      fensterErwachen: {
        name: 'Fenster Erwachen',
        description: 'Fenster leuchten nacheinander auf wie beim Sonnenaufgang',
        targetGroup: 'fenster',
        pattern: 'sequence',
        effect: {
          type: 'windowGlow',
          parameters: {
            glowColor: { value: '#FFE4B5' },
            intensity: { value: 0.9 },
            innerGlow: { value: true },
            spreadRadius: { value: 8 }
          }
        },
        timing: {
          duration: 8000,
          elementDuration: 3000,
          beatSync: true,
          quantization: 'bar'
        }
      },

      nachtbeleuchtung: {
        name: 'Nachtbeleuchtung',
        description: 'Gemütliches warmes Licht in allen Fenstern',
        targetGroup: 'fenster',
        pattern: 'simultaneous',
        effect: {
          type: 'warmWindowLight',
          parameters: {
            warmColor: { value: '#FFA500' },
            intensity: { value: 0.7 },
            flicker: { value: 0.1 },
            opacity: { value: 0.8 }
          }
        },
        timing: {
          duration: 60000,
          elementDuration: 60000,
          beatSync: false,
          quantization: 'immediate'
        }
      },

      fensterStrobe: {
        name: 'Fenster Blitzlicht',
        description: 'Helle Blitze durch die Fenster (Gewitter-Effekt)',
        targetGroup: 'fenster',
        pattern: 'random',
        effect: {
          type: 'lightningFlash',
          parameters: {
            flashColor: { value: '#FFFFFF' },
            intensity: { value: 1.0 },
            duration: { value: 200 },
            randomDelay: { value: 500 }
          }
        },
        timing: {
          duration: 4000,
          elementDuration: 200,
          beatSync: true,
          quantization: 'beat'
        }
      },

      stockwerkWelle: {
        name: 'Stockwerk Welle',
        description: 'Welle läuft durch beide Stockwerke',
        targetGroup: 'all',
        pattern: 'stockwerkWave',
        effect: {
          type: 'colorWave',
          parameters: {
            waveColor: { value: '#00FFFF' },
            intensity: { value: 0.8 },
            waveSpeed: { value: 1.5 }
          }
        },
        timing: {
          duration: 6000,
          elementDuration: 1500,
          beatSync: true,
          quantization: 'bar'
        }
      },

      fensterTanzen: {
        name: 'Fenster Tanzen',
        description: 'Abwechselndes Aufleuchten der Fenster im Rhythmus',
        targetGroup: 'fenster',
        pattern: 'alternatingStockwerk',
        effect: {
          type: 'rhythmicPulse',
          parameters: {
            color1: { value: '#FF6B35' },
            color2: { value: '#4ECDC4' },
            intensity: { value: 0.9 },
            speed: { value: 2.0 }
          }
        },
        timing: {
          duration: 4000,
          elementDuration: 2000,
          beatSync: true,
          quantization: 'beat'
        }
      },

      hausbeleuchtung: {
        name: 'Komplette Hausbeleuchtung',
        description: 'Vollständige Illumination aller Bereiche',
        targetGroup: 'all',
        pattern: 'architekturalWave',
        effect: {
          type: 'architecturalGlow',
          parameters: {
            balkenColor: { value: '#8B4513' },
            gefachColor: { value: '#F5E6D3' },
            fensterColor: { value: '#FFE4B5' },
            intensity: { value: 0.8 }
          }
        },
        timing: {
          duration: 8000,
          elementDuration: 6000,
          beatSync: true,
          quantization: '2-bar'
        }
      },

      fensterSpiegelung: {
        name: 'Fenster Spiegelung',
        description: 'Symmetrische Effekte zwischen linken und rechten Fenstern',
        targetGroup: 'fenster',
        pattern: 'mirror',
        effect: {
          type: 'mirrorGlow',
          parameters: {
            primaryColor: { value: '#00FF88' },
            secondaryColor: { value: '#FF0088' },
            intensity: { value: 0.7 },
            symmetryAxis: { value: 'vertical' }
          }
        },
        timing: {
          duration: 5000,
          elementDuration: 2500,
          beatSync: true,
          quantization: 'bar'
        }
      },

      structureWave: {
        name: 'Struktur Welle',
        targetGroup: 'all',
        pattern: 'wave',
        effect: {
          type: 'rainbow',
          parameters: {
            speed: { value: 1.0 },
            intensity: { value: 0.8 },
            cycles: { value: 2 }
          }
        },
        timing: {
          duration: 6000,
          elementDuration: 1500,
          beatSync: true,
          quantization: 'bar'
        }
      },

      centerOut: {
        name: 'Von Innen Nach Außen',
        targetGroup: 'all',
        pattern: 'spiral',
        effect: {
          type: 'pulse',
          parameters: {
            color: { value: '#00ff88' },
            intensity: { value: 0.9 },
            speed: { value: 2.5 }
          }
        },
        timing: {
          duration: 4000,
          elementDuration: 800,
          beatSync: true,
          quantization: 'bar'
        }
      },

      // BPM-Synchronized Effects
      beatStrobeAll: {
        name: '🎵 Beat Strobe All',
        targetGroup: 'all',
        pattern: 'simultaneous',
        effect: { type: 'strobe', parameters: { color: '#ffffff', frequency: 4, intensity: 1.0 } },
        timing: {
          duration: 8000,
          elementDuration: 250,
          beatSync: true,
          quantization: 'beat'
        }
      },

      barRainbow: {
        name: '🌈 Bar Rainbow Wave',
        targetGroup: 'all',
        pattern: 'sequential',
        effect: { type: 'rainbow', parameters: { speed: 2.0, intensity: 0.8, cycles: 1 } },
        timing: {
          duration: 16000,
          elementDuration: 4000,
          beatSync: true,
          quantization: 'bar'
        }
      },

      beatPulseWindows: {
        name: '💓 Beat Pulse Windows',
        targetGroup: 'fenster',
        pattern: 'alternating',
        effect: { type: 'pulse', parameters: { color: '#ff6b6b', intensity: 0.9 } },
        timing: {
          duration: 2000,
          elementDuration: 500,
          beatSync: true,
          quantization: 'beat'
        }
      },

      bassDropEffect: {
        name: '🔥 Bass Drop Effect',
        targetGroup: 'all',
        pattern: 'simultaneous',
        effect: { type: 'colorFade', parameters: { from: '#000000', to: '#ff0000', speed: 0.5 } },
        timing: {
          duration: 1000,
          elementDuration: 1000,
          beatSync: true,
          quantization: 'immediate'
        }
      },

      buildUpWave: {
        name: '📈 Build-Up Wave',
        targetGroup: 'all',
        pattern: 'sequential',
        effect: { type: 'breathe', parameters: { breathRate: 0.5, depth: 0.5, asymmetry: 0.3 } },
        timing: {
          duration: 32000,
          elementDuration: 8000,
          beatSync: true,
          quantization: '4-bar'
        }
      },

      // Chase Effects (Stop Previous) - Infinite Loops
      rainbowChase: {
        name: '🌈 Rainbow Chase ∞',
        targetGroup: 'all',
        pattern: 'sequentialChase',
        effect: { type: 'rainbow', parameters: { speed: 1.5, intensity: 0.9, cycles: 1 } },
        timing: {
          duration: 60000, // Long duration for multiple cycles
          elementDuration: 1500,
          beatSync: true,
          quantization: 'bar'
        }
      },

      windowChase: {
        name: '🪟 Window Chase ∞',
        targetGroup: 'fenster',
        pattern: 'sequentialChase',
        effect: { type: 'windowGlow', parameters: { glowColor: '#FFE4B5', intensity: 0.9, spreadRadius: 8 } },
        timing: {
          duration: 30000, // Long duration for multiple cycles
          elementDuration: 800,
          beatSync: true,
          quantization: 'beat'
        }
      },

      pulseChase: {
        name: '💓 Pulse Chase ∞',
        targetGroup: 'all',
        pattern: 'alternatingChase',
        effect: { type: 'pulse', parameters: { color: '#ff6b6b', intensity: 0.8 } },
        timing: {
          duration: 45000, // Long duration for multiple cycles
          elementDuration: 600,
          beatSync: true,
          quantization: 'beat'
        }
      },

      strobeChase: {
        name: '⚡ Strobe Chase ∞',
        targetGroup: 'all',
        pattern: 'waveChase',
        effect: { type: 'strobe', parameters: { color: '#ffffff', frequency: 8, intensity: 1.0 } },
        timing: {
          duration: 40000, // Long duration for multiple cycles
          elementDuration: 300,
          beatSync: true,
          quantization: 'bar'
        }
      },

      infiniteRainbow: {
        name: '🔄 Infinite Rainbow Loop',
        targetGroup: 'all',
        pattern: 'sequentialChase',
        effect: { type: 'rainbow', parameters: { speed: 2.0, intensity: 1.0, cycles: 1 } },
        timing: {
          duration: 120000, // 2 minutes of continuous looping
          elementDuration: 1000,
          beatSync: false,
          quantization: 'immediate'
        }
      }
    };
  }
}

// ==================== ENHANCED EFFECT ENGINE HOOK ====================

const useEffectEngine = (deckA, deckB, crossfadeValue = 50, masterVolume = 100, beatEngine) => {
  const animationFrameRef = useRef();
  const activeAnimations = useRef(new Map());
  const deckAnimations = useRef({ A: new Set(), B: new Set() });
  const [engineStats, setEngineStats] = useState({ fps: 60, activeCount: 0 });

  const getEffectiveSpeed = useCallback((baseDuration, deck) => {
    if (deck.isBpmSynced && beatEngine) {
      const bpmMultiplier = deck.bpm / 120;
      return baseDuration / (bpmMultiplier * deck.playbackSpeed);
    } else {
      return baseDuration / deck.playbackSpeed;
    }
  }, [beatEngine]);

  const getCrossfadeMix = useCallback(() => {
    return crossfadeValue / 100;
  }, [crossfadeValue]);

  const getDeckVolume = useCallback((deck) => {
    const crossfadeMix = getCrossfadeMix();
    if (deck === 'A') {
      return (1 - crossfadeMix) * (deckA.volume / 100);
    } else {
      return crossfadeMix * (deckB.volume / 100);
    }
  }, [deckA.volume, deckB.volume, getCrossfadeMix]);

  const applyColorToElement = useCallback((element, color) => {
    if (element.tagName === 'g') {
      const children = element.querySelectorAll('rect, circle, path, polygon, ellipse');
      children.forEach(child => {
        child.setAttribute('fill', color);
      });
    } else if (element.tagName === 'rect' || element.tagName === 'circle' ||
               element.tagName === 'path' || element.tagName === 'polygon' ||
               element.tagName === 'ellipse') {
      element.setAttribute('fill', color);
    } else {
      element.style.backgroundColor = color;
    }
  }, []);

  const applyEffectFrame = useCallback((element, effect, progress, deck = 'A') => {
    const eased = applyEasing(progress, effect.easing || 'linear');
    let transforms = [];

    switch (effect.type) {
      case 'colorFade':
        const colorProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
        applyColorToElement(element, interpolateColor(effect.from || '#ff0000', effect.to || '#0000ff', colorProgress));
        break;

      case 'rainbow':
        const rainbowSpeed = effect.speed || 1.0;
        const rainbowIntensity = effect.intensity || 0.8;
        const rainbowCycles = effect.cycles || 2;
        const rainbowSaturation = effect.saturation || 100;
        const rainbowLightness = effect.lightness || 50;

        const hue = (progress * 360 * rainbowCycles * rainbowSpeed) % 360;
        const saturation = Math.max(0, Math.min(100, rainbowSaturation * rainbowIntensity));
        const lightness = Math.max(0, Math.min(100, rainbowLightness * rainbowIntensity));

        applyColorToElement(element, `hsl(${hue}, ${saturation}%, ${lightness}%)`);
        break;

      case 'strobe':
        const strobeFreq = effect.frequency || 10;
        const strobeIntensity = effect.intensity || 1.0;
        const strobeDutyCycle = effect.dutyCycle || 0.5;

        const strobePhase = (progress * strobeFreq) % 1;
        const isOn = strobePhase < strobeDutyCycle;
        const strobeColor = effect.color || '#ffffff';

        if (isOn) {
          applyColorToElement(element, strobeColor);
          element.style.opacity = strobeIntensity;
        } else {
          applyColorToElement(element, '#000000');
          element.style.opacity = 0.1;
        }
        break;

      case 'pulse':
        const pulseIntensity = effect.intensity || 0.8;
        const pulseSpeedParam = effect.speed || 1.5;
        const pulseWidth = effect.pulseWidth || 0.5;
        const pulseBrightness = effect.brightness || 1.0;

        // Enhanced pulse calculation with width control
        const pulsePhase = progress * Math.PI * 4 * pulseSpeedParam;
        const pulseCycle = (Math.sin(pulsePhase) + 1) / 2; // 0 to 1
        const pulseValue = pulseCycle < pulseWidth ?
          (pulseCycle / pulseWidth) * pulseIntensity :
          ((1 - pulseCycle) / (1 - pulseWidth)) * pulseIntensity;

        const pulseBaseColor = '#000000';
        const pulseTargetColor = effect.color || '#00ff00';
        const pulseFinalColor = interpolateColor(pulseBaseColor, pulseTargetColor, Math.max(0, Math.min(1, pulseValue)));

        applyColorToElement(element, pulseFinalColor);

        // Apply brightness if different from default
        if (pulseBrightness !== 1.0) {
          element.style.filter = `brightness(${pulseBrightness * 100}%)`;
        }
        break;

      case 'scale':
        const scaleProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
        const scaleValue = 1 + ((effect.scale || 1.2) - 1) * scaleProgress;
        transforms.push(`scale(${scaleValue})`);
        break;

      case 'rotate':
        transforms.push(`rotate(${(effect.degrees || 360) * progress}deg)`);
        break;

      case 'slide':
        const slideProgress = Math.sin(progress * Math.PI * 2);
        const slideX = (effect.x || 0) * slideProgress;
        const slideY = (effect.y || 0) * slideProgress;
        transforms.push(`translate(${slideX}px, ${slideY}px)`);
        break;

      case 'shake':
        const intensity = (effect.intensity || 5) * (1 - eased);
        const x = (Math.random() - 0.5) * intensity;
        const y = (Math.random() - 0.5) * intensity;
        transforms.push(`translate(${x}px, ${y}px)`);
        break;

      case 'opacity':
        const opacityProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
        const fromOpacity = effect.from !== undefined ? effect.from : 1;
        const toOpacity = effect.to !== undefined ? effect.to : 0;
        element.style.opacity = fromOpacity + (toOpacity - fromOpacity) * opacityProgress;
        break;

      case 'fadeIn':
        const fadeInProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
        element.style.opacity = fadeInProgress;
        break;

      case 'fadeOut':
        const fadeOutProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
        element.style.opacity = 1 - fadeOutProgress;
        break;

      case 'flicker':
        const flickerIntensity = effect.intensity || 0.5;
        const flickerValue = Math.random() > flickerIntensity ? 1 : 0.1;
        element.style.opacity = flickerValue;
        break;

      // Enhanced atmospheric effects
      case 'breathe':
        const breathRate = effect.breathRate || 0.2;
        const depth = effect.depth || 0.3;
        const asymmetry = effect.asymmetry || 0.5;
        
        const cyclePos = (progress * breathRate) % 1;
        let breathValue;
        
        if (cyclePos < asymmetry) {
          breathValue = Math.sin((cyclePos / asymmetry) * Math.PI * 0.5);
        } else {
          const exhalePos = (cyclePos - asymmetry) / (1 - asymmetry);
          breathValue = Math.cos(exhalePos * Math.PI * 0.5);
        }
        
        const opacity = 1 - (depth * 0.5) + (depth * 0.5 * breathValue);
        element.style.opacity = opacity;
        
        const brightness = 100 - (depth * 20) + (depth * 20 * breathValue);
        element.style.filter = `brightness(${brightness}%)`;
        break;

      case 'warmCool':
        const warmColor = effect.warmColor || '#ff8844';
        const coolColor = effect.coolColor || '#4488ff';
        const cycleTime = effect.cycleTime || 15000;
        const smoothness = effect.smoothness || 0.8;
        
        const cycleProgress = (progress * 1000 / cycleTime) % 1;
        const smoothed = smoothness * Math.sin(cycleProgress * Math.PI * 2) + (1 - smoothness) * Math.sin(cycleProgress * Math.PI * 4) * 0.3;
        const colorMix = (smoothed + 1) / 2;
        
        const interpolatedColor = interpolateColor(warmColor, coolColor, colorMix);
        applyColorToElement(element, interpolatedColor);
        break;

      case 'organicFlow':
        const flowSpeed = effect.flowSpeed || 0.5;
        const turbulence = effect.turbulence || 0.3;
        const scale = effect.scale || 100;

        const time = progress * flowSpeed;
        const flowX = Math.sin(time * 0.7 + Math.sin(time * 1.3) * turbulence) * scale;
        const flowY = Math.cos(time * 0.5 + Math.cos(time * 1.7) * turbulence) * scale * 0.7;

        const rotation = time * 15 + Math.sin(time * 0.3) * 20 * turbulence;

        transforms.push(`translate(${flowX}px, ${flowY}px) rotate(${rotation}deg)`);
        break;

      // Fenster-spezifische Effekte
      case 'windowGlow':
        const glowColor = effect.glowColor || '#FFE4B5';
        const glowIntensity = effect.intensity || 0.9;
        const innerGlow = effect.innerGlow !== undefined ? effect.innerGlow : true;
        const spreadRadius = effect.spreadRadius || 8;
        const pulseRate = effect.pulseRate || 1.0;

        if (innerGlow) {
          // Enhanced pulsing with configurable rate
          const pulsePhase = progress * Math.PI * 2 * pulseRate;
          const currentGlowIntensity = glowIntensity * (0.5 + 0.5 * Math.sin(pulsePhase));

          // Apply glow with variable spread radius
          const glowSpread = spreadRadius * (0.8 + 0.4 * currentGlowIntensity);
          element.style.filter = `drop-shadow(0 0 ${glowSpread}px ${glowColor}) brightness(${100 + currentGlowIntensity * 50}%)`;

          const fensterRect = element.querySelector('rect');
          if (fensterRect) {
            const rectOpacity = 0.3 + 0.7 * currentGlowIntensity;
            fensterRect.setAttribute('fill-opacity', rectOpacity);
          }
        } else {
          // Static glow without pulsing
          element.style.filter = `drop-shadow(0 0 ${spreadRadius}px ${glowColor}) brightness(${100 + glowIntensity * 50}%)`;
        }
        break;

      case 'warmWindowLight':
        const warmLightColor = effect.warmColor || '#FFA500';
        const lightIntensity = effect.intensity || 0.7;
        const flicker = effect.flicker || 0.1;
        const lightOpacity = effect.opacity || 0.8;

        const warmFlickerValue = 1 + (Math.random() - 0.5) * flicker;
        const currentLightIntensity = lightIntensity * warmFlickerValue;
        
        element.style.filter = `drop-shadow(0 0 12px ${warmLightColor})`;
        element.style.opacity = lightOpacity * currentLightIntensity;
        
        const fensterRect = element.querySelector('rect');
        if (fensterRect) {
          fensterRect.setAttribute('fill', warmLightColor);
          fensterRect.setAttribute('fill-opacity', currentLightIntensity);
        }
        break;

      case 'lightningFlash':
        const flashColor = effect.flashColor || '#FFFFFF';
        const flashIntensity = effect.intensity || 1.0;
        const flashDuration = effect.duration || 200;
        
        const shouldFlash = Math.random() < 0.1;
        
        if (shouldFlash) {
          element.style.filter = `drop-shadow(0 0 20px ${flashColor}) brightness(300%)`;
          element.style.opacity = flashIntensity;
          
          setTimeout(() => {
            element.style.filter = '';
            element.style.opacity = '';
          }, flashDuration);
        }
        break;

      case 'rhythmicPulse':
        const color1 = effect.color1 || '#FF6B35';
        const color2 = effect.color2 || '#4ECDC4';
        const pulseSpeed = effect.speed || 2.0;
        
        const rhythmicPulsePhase = (Math.sin(progress * Math.PI * 2 * pulseSpeed) + 1) / 2;
        const currentColor = interpolateColor(color1, color2, rhythmicPulsePhase);
        const currentIntensity = effect.intensity * (0.5 + 0.5 * rhythmicPulsePhase);
        
        applyColorToElement(element, currentColor);
        element.style.filter = `drop-shadow(0 0 8px ${currentColor})`;
        element.style.opacity = currentIntensity;
        break;

      case 'architecturalGlow':
        const balkenColor = effect.balkenColor || '#8B4513';
        const gefachColor = effect.gefachColor || '#F5E6D3';
        const fensterColor = effect.fensterColor || '#FFE4B5';
        const archIntensity = effect.intensity || 0.8;
        
        let targetColor;
        let glowRadius;
        
        if (element.id.startsWith('B')) {
          targetColor = balkenColor;
          glowRadius = 6;
        } else if (element.id.startsWith('G')) {
          targetColor = gefachColor;
          glowRadius = 4;
        } else if (element.id.startsWith('F')) {
          targetColor = fensterColor;
          glowRadius = 10;
        }
        
        const archGlowIntensity = archIntensity * (0.7 + 0.3 * Math.sin(progress * Math.PI * 2));
        element.style.filter = `drop-shadow(0 0 ${glowRadius}px ${targetColor})`;
        applyColorToElement(element, targetColor);
        element.style.opacity = archGlowIntensity;
        break;

      case 'mirrorGlow':
        const primaryColor = effect.primaryColor || '#00FF88';
        const secondaryColor = effect.secondaryColor || '#FF0088';
        const mirrorIntensity = effect.intensity || 0.7;

        let isLeftSide = false;
        try {
          if (element.getBBox && typeof element.getBBox === 'function') {
            const bbox = element.getBBox();
            isLeftSide = bbox.x + bbox.width / 2 < 400;
          }
        } catch (error) {
          console.warn('Failed to get bounding box for mirror effect:', error);
        }

        const mirrorColor = isLeftSide ? primaryColor : secondaryColor;
        
        const mirrorGlowIntensity = mirrorIntensity * (0.5 + 0.5 * Math.sin(progress * Math.PI * 2));
        
        applyColorToElement(element, mirrorColor);
        element.style.filter = `drop-shadow(0 0 12px ${mirrorColor})`;
        element.style.opacity = mirrorGlowIntensity;
        break;

      default:
        console.warn(`Unknown effect type: ${effect.type}`);
        break;
    }

    if (transforms.length > 0) {
      element.style.transform = transforms.join(' ');
    }
  }, [applyColorToElement]);

  const startAnimation = useCallback((elementId, effect, duration = 2000, deck = 'A') => {
    const startTime = performance.now();
    const animationId = `${elementId}_${Date.now()}`;

    const deckData = deck === 'A' ? deckA : deckB;
    const effectiveDuration = getEffectiveSpeed(duration, deckData);

    const deckElementId = `${elementId}_deck${deck}`;

    // Store animation metadata for real-time updates
    animationMetadata.current.set(animationId, {
      elementId,
      effect: { ...effect },
      duration: effectiveDuration,
      deck,
      startTime
    });

    const designElement = document.getElementById(elementId);
    if (designElement) {
      designElement.style.opacity = '0.1';
    }

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const cycleProgress = (elapsed % effectiveDuration) / effectiveDuration;

      const element = document.getElementById(deckElementId);
      if (element) {
        // Get current effect parameters from metadata (for real-time updates)
        const currentMetadata = animationMetadata.current.get(animationId);
        const currentEffect = currentMetadata ? currentMetadata.effect : effect;

        applyEffectFrame(element, currentEffect, cycleProgress, deck);
      } else {
        console.warn('Element not found:', deckElementId);
      }

      if (activeAnimations.current.has(animationId)) {
        activeAnimations.current.set(animationId, requestAnimationFrame(animate));
      }
    };

    activeAnimations.current.set(animationId, requestAnimationFrame(animate));
    deckAnimations.current[deck].add(animationId);
    setEngineStats(prev => ({ ...prev, activeCount: activeAnimations.current.size }));

    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'startAnimation',
        elementId: deckElementId,
        effect,
        duration: effectiveDuration,
        animationId,
        deck,
        crossfadeValue,
        masterVolume
      }, '*');
    }

    return animationId;
  }, [applyEffectFrame, getEffectiveSpeed, deckA, deckB]);

  const startBeatSyncAnimation = useCallback((elementId, effect, quantization = 'beat') => {
    if (!beatEngine) {
      return startAnimation(elementId, effect, effect.duration || 2000);
    }

    const startEffect = () => {
      const beatDuration = 60000 / beatEngine.bpm;
      const beatMultiplier = {
        'beat': 1,
        'bar': 4,
        '2-bar': 8,
        '4-bar': 16
      }[quantization] || 1;
      
      const syncedDuration = beatDuration * beatMultiplier;
      return startAnimation(elementId, effect, syncedDuration);
    };

    beatEngine.quantizeToGrid(startEffect, quantization);
  }, [startAnimation, beatEngine]);

  const stopAnimation = useCallback((animationId) => {
    const frameId = activeAnimations.current.get(animationId);
    if (frameId) {
      cancelAnimationFrame(frameId);
      activeAnimations.current.delete(animationId);

      // Clean up metadata
      animationMetadata.current.delete(animationId);

      deckAnimations.current.A.delete(animationId);
      deckAnimations.current.B.delete(animationId);

      setEngineStats(prev => ({ ...prev, activeCount: activeAnimations.current.size }));

      if (window.projectionWindow && !window.projectionWindow.closed) {
        window.projectionWindow.postMessage({
          type: 'stopAnimation',
          animationId
        }, '*');
      }
    }
  }, []);

  // Store animation metadata for real-time updates
  const animationMetadata = useRef(new Map());

  const updateAnimationParameters = useCallback((animationId, newParameters) => {
    const metadata = animationMetadata.current.get(animationId);
    if (metadata) {
      // Update the stored effect parameters
      metadata.effect = {
        ...metadata.effect,
        ...newParameters
      };

      // Send update to projection window
      if (window.projectionWindow && !window.projectionWindow.closed) {
        window.projectionWindow.postMessage({
          type: 'updateAnimationParameters',
          animationId,
          parameters: newParameters
        }, '*');
      }
    }
  }, []);

  const stopDeckAnimations = useCallback((deck) => {
    const deckAnimationIds = Array.from(deckAnimations.current[deck]);
    deckAnimationIds.forEach(animationId => {
      const frameId = activeAnimations.current.get(animationId);
      if (frameId) {
        cancelAnimationFrame(frameId);
        activeAnimations.current.delete(animationId);
      }

      const elementId = animationId.split('_')[0];
      const designElement = document.getElementById(elementId);
      if (designElement) {
        designElement.style.opacity = '';
      }
    });
    deckAnimations.current[deck].clear();
    setEngineStats(prev => ({ ...prev, activeCount: activeAnimations.current.size }));

    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'stopDeckAnimations',
        deck
      }, '*');
    }
  }, []);

  const stopAllAnimations = useCallback(() => {
    activeAnimations.current.forEach(frameId => cancelAnimationFrame(frameId));
    activeAnimations.current.clear();
    deckAnimations.current.A.clear();
    deckAnimations.current.B.clear();
    setEngineStats(prev => ({ ...prev, activeCount: 0 }));

    const designLayer = document.getElementById('design-layer');
    if (designLayer) {
      designLayer.querySelectorAll('[id]').forEach(element => {
        element.style.opacity = '';
      });
    }

    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'stopAllAnimations'
      }, '*');
    }
  }, []);

  const startGroupEffect = useCallback((groupEffect, groupEngine) => {
    const { targetGroup, pattern, effect, timing } = groupEffect;
    const elements = groupEngine.groups[targetGroup];

    if (!elements || elements.length === 0) {
      console.warn(`No elements found in group: ${targetGroup}`);
      return null;
    }

    console.log(`Starting group effect "${groupEffect.name}" on ${elements.length} elements in group "${targetGroup}"`);

    const patternInstance = groupEngine.getGroupPatterns()[pattern];
    if (!patternInstance) {
      console.warn(`Pattern "${pattern}" not found`);
      return null;
    }

    const intervals = patternInstance.apply(elements, timing);
    const animationIds = [];

    intervals.forEach((interval, index) => {
      const startEffect = () => {
        // Use the actual effect parameters from the group effect
        const effectToApply = {
          ...effect,
          ...effect.parameters // Merge parameters into the effect object
        };

        const animationId = timing.beatSync && beatEngine
          ? startBeatSyncAnimation(interval.elementId, effectToApply, timing.quantization)
          : startAnimation(interval.elementId, effectToApply, interval.duration || timing.elementDuration);

        if (animationId) {
          animationIds.push(animationId);
        }
      };

      if (interval.startTime === 0) {
        if (timing.beatSync && beatEngine) {
          beatEngine.quantizeToGrid(startEffect, timing.quantization);
        } else {
          startEffect();
        }
      } else {
        setTimeout(startEffect, interval.startTime);
      }
    });

    return {
      id: `group_${Date.now()}`,
      animationIds,
      groupEffect,
      targetGroup,
      elementCount: elements.length
    };
  }, [startAnimation, startBeatSyncAnimation, beatEngine]);

  const updateCrossfade = useCallback((newCrossfadeValue, newMasterVolume) => {
    if (window.projectionWindow && !window.projectionWindow.closed) {
      window.projectionWindow.postMessage({
        type: 'updateCrossfade',
        crossfadeValue: newCrossfadeValue,
        masterVolume: newMasterVolume
      }, '*');
    }
  }, []);

  const applyEasing = (t, type) => {
    switch (type) {
      case 'easeIn': return t * t;
      case 'easeOut': return 1 - (1 - t) * (1 - t);
      case 'easeInOut': return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
      case 'bounce': return 1 - Math.abs(Math.sin(t * Math.PI * 2) * (1 - t));
      default: return t;
    }
  };

  const interpolateColor = (from, to, progress) => {
    const fromRgb = hexToRgb(from);
    const toRgb = hexToRgb(to);
    const r = Math.round(fromRgb.r + (toRgb.r - fromRgb.r) * progress);
    const g = Math.round(fromRgb.g + (toRgb.g - fromRgb.g) * progress);
    const b = Math.round(fromRgb.b + (toRgb.b - fromRgb.b) * progress);
    return `rgb(${r}, ${g}, ${b})`;
  };

  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 0, g: 0, b: 0 };
  };

  useEffect(() => {
    return () => stopAllAnimations();
  }, [stopAllAnimations]);

  return {
    startAnimation,
    startBeatSyncAnimation,
    startGroupEffect,
    stopAnimation,
    stopDeckAnimations,
    stopAllAnimations,
    updateCrossfade,
    updateAnimationParameters,
    engineStats
  };
};

// ==================== NOTIFICATION COMPONENT ====================

const NotificationSystem = () => {
  const { state, dispatch } = useContext(AppContext);
  const { notifications } = state.ui;

  useEffect(() => {
    notifications.forEach(notification => {
      if (!notification.persistent) {
        setTimeout(() => {
          dispatch({ type: 'REMOVE_NOTIFICATION', payload: notification.id });
        }, notification.duration || 3000);
      }
    });
  }, [notifications, dispatch]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={`flex items-center gap-2 px-4 py-3 rounded-lg shadow-lg transition-all transform ${
            notification.type === 'error' ? 'bg-red-600' :
            notification.type === 'success' ? 'bg-green-600' :
            notification.type === 'warning' ? 'bg-yellow-600' :
            'bg-blue-600'
          } text-white`}
        >
          {notification.type === 'error' && <AlertCircle size={20} />}
          {notification.type === 'success' && <CheckCircle size={20} />}
          {notification.type === 'warning' && <AlertCircle size={20} />}
          <span className="flex-1">{notification.message}</span>
          <button
            onClick={() => dispatch({ type: 'REMOVE_NOTIFICATION', payload: notification.id })}
            className="hover:opacity-70"
          >
            <X size={16} />
          </button>
        </div>
      ))}
    </div>
  );
};

// ==================== PERFORMANCE MONITOR ====================

const PerformanceMonitor = () => {
  const { state } = useContext(AppContext);
  const { fps, frameTime, activeAnimations, memoryUsage } = state.performance;
  const [expanded, setExpanded] = useState(false);

  return (
    <div className={`fixed bottom-4 left-4 bg-gray-800 rounded-lg shadow-lg transition-all ${
      expanded ? 'w-64' : 'w-32'
    }`}>
      <button
        onClick={() => setExpanded(!expanded)}
        className="w-full px-3 py-2 flex items-center justify-between text-white hover:bg-gray-700 rounded-lg"
      >
        <Activity size={16} />
        <span className="text-sm font-mono">{fps} FPS</span>
      </button>
      
      {expanded && (
        <div className="px-3 py-2 space-y-1 text-xs text-gray-300 border-t border-gray-700">
          <div className="flex justify-between">
            <span>Frame Time:</span>
            <span className="font-mono">{frameTime.toFixed(2)}ms</span>
          </div>
          <div className="flex justify-between">
            <span>Animations:</span>
            <span className="font-mono">{activeAnimations}</span>
          </div>
          <div className="flex justify-between">
            <span>Memory:</span>
            <span className="font-mono">{(memoryUsage / 1024 / 1024).toFixed(1)}MB</span>
          </div>
        </div>
      )}
    </div>
  );
};

// ==================== BEAT SYNC PANEL ====================

const BeatSyncPanel = ({ beatEngine, onBeatSync }) => {
  const { state, dispatch } = useContext(AppContext);
  const [manualBPM, setManualBPM] = useState(120);

  return (
    <div className="bg-gray-800 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-bold text-white">Beat Sync</h3>
        <div className="flex items-center gap-2">
          <BeatIndicator beatEngine={beatEngine} />
          <button
            onClick={() => {/* Toggle sync */}}
            className="px-3 py-1 rounded text-sm font-bold bg-green-600 text-white"
          >
            SYNC ON
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-300">Auto Detection</label>
          <button
            onClick={async () => {
              if (state.beatEngine.isListening) {
                beatEngine.stopListening();
              } else {
                await beatEngine.initAudioAnalysis();
              }
            }}
            className={`w-full py-2 rounded-lg font-bold transition-all ${
              state.beatEngine.isListening
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {state.beatEngine.isListening ? '🛑 Stop Listen' : '🎵 Start Listen'}
          </button>
          
          {state.beatEngine.isListening && (
            <div className="text-xs text-gray-400 text-center">
              Confidence: {Math.round(state.beatEngine.confidence * 100)}%
            </div>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-300">Manual BPM</label>
          <div className="flex gap-2">
            <input
              type="number"
              min="30"
              max="300"
              value={manualBPM}
              onChange={(e) => setManualBPM(parseInt(e.target.value))}
              className="flex-1 px-3 py-2 bg-gray-700 rounded text-center font-mono"
            />
            <button
              onClick={() => beatEngine.setBPM(manualBPM)}
              className="px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm font-bold"
            >
              Set
            </button>
          </div>
        </div>
      </div>

      <div className="bg-gray-700 rounded-lg p-4">
        <div className="text-center">
          <div className="text-3xl font-mono font-bold text-blue-400 mb-2">
            {state.beatEngine.bpm}
          </div>
          <div className="text-sm text-gray-400">BPM</div>
          <div className="flex justify-center gap-4 mt-3 text-xs text-gray-400">
            <span>Beat: {state.beatEngine.beatCount}</span>
            <span>Bar: {state.beatEngine.barCount}</span>
          </div>
        </div>
      </div>

      <button
        onClick={() => beatEngine.tapBeat()}
        className="w-full py-3 bg-purple-600 hover:bg-purple-700 rounded-lg font-bold text-lg transition-all transform hover:scale-105"
      >
        🥁 TAP BEAT
      </button>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-300">Effect Quantization</label>
        <div className="grid grid-cols-4 gap-2">
          {['immediate', 'beat', 'bar', '2-bar'].map(option => (
            <button
              key={option}
              onClick={() => dispatch({ 
                type: 'UPDATE_BEAT_ENGINE', 
                payload: { quantization: option } 
              })}
              className={`py-2 px-3 rounded text-sm font-bold transition-colors ${
                state.beatEngine.quantization === option
                  ? 'bg-yellow-600 text-white'
                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }`}
            >
              {option.charAt(0).toUpperCase() + option.slice(1)}
            </button>
          ))}
        </div>
      </div>

      <BeatGrid beatEngine={beatEngine} />
    </div>
  );
};

const BeatIndicator = ({ beatEngine }) => {
  const { state } = useContext(AppContext);
  const [beatFlash, setBeatFlash] = useState(false);
  
  useEffect(() => {
    const handleBeat = () => {
      setBeatFlash(true);
      setTimeout(() => setBeatFlash(false), 100);
    };
    
    if (beatEngine) {
      beatEngine.onBeat('ui-indicator', handleBeat);
      
      return () => beatEngine.beatCallbacks.delete('ui-indicator');
    }
  }, [beatEngine]);

  return (
    <div className={`w-4 h-4 rounded-full transition-all duration-100 ${
      beatFlash ? 'bg-red-500 scale-125 shadow-lg shadow-red-500/50' : 'bg-gray-600'
    }`} />
  );
};

const BeatGrid = ({ beatEngine }) => {
  const { state } = useContext(AppContext);
  const [currentBeat, setCurrentBeat] = useState(0);
  
  useEffect(() => {
    const handleBeat = (beatCount) => {
      setCurrentBeat(beatCount % 16);
    };
    
    if (beatEngine) {
      beatEngine.onBeat('beat-grid', handleBeat);
      
      return () => beatEngine.beatCallbacks.delete('beat-grid');
    }
  }, [beatEngine]);

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium text-gray-300">Beat Grid (4 Bars)</div>
      <div className="grid grid-cols-16 gap-1">
        {Array.from({ length: 16 }, (_, i) => (
          <div
            key={i}
            className={`h-3 rounded transition-all ${
              i === currentBeat
                ? 'bg-red-500 shadow-lg shadow-red-500/50'
                : i % 4 === 0
                ? 'bg-yellow-600'
                : 'bg-gray-600'
            }`}
          />
        ))}
      </div>
      <div className="grid grid-cols-4 gap-1 text-xs text-gray-400 text-center">
        <span>Bar 1</span>
        <span>Bar 2</span>
        <span>Bar 3</span>
        <span>Bar 4</span>
      </div>
    </div>
  );
};

// Effect type display names
const effectTypes = {
  pulse: { name: 'Pulsieren' },
  colorFade: { name: 'Farbverlauf' },
  rainbow: { name: 'Regenbogen' },
  strobe: { name: 'Stroboskop' },
  breathe: { name: 'Atmen' },
  windowGlow: { name: 'Fenster Glühen' },
  warmWindowLight: { name: 'Warmes Licht' },
  scale: { name: 'Skalierung' },
  rotate: { name: 'Rotation' },
  slide: { name: 'Gleiten' },
  shadowCast: { name: 'Schattenwurf' },
  architecturalGlow: { name: 'Architektur Glühen' },
  mirrorGlow: { name: 'Spiegel Glühen' }
};

// ==================== EFFECTS ACCORDION ====================

const EffectsAccordion = ({
  selectedElement,
  currentSequence,
  groupEngine,
  effectEngine,
  onAddEffect,
  onAddGroupEffectToSequence
}) => {
  const [openSections, setOpenSections] = useState({
    effectLibrary: true,
    groupEffects: false
  });

  const toggleSection = (section) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <div className="space-y-4">
      {/* Effect Library Section */}
      {selectedElement && (
        <div className="bg-gray-700 rounded-lg overflow-hidden">
          <button
            onClick={() => toggleSection('effectLibrary')}
            className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold flex items-center justify-between transition-all"
          >
            <div className="flex items-center gap-2">
              <span>🎨</span>
              <span>Effekt-Bibliothek</span>
              <span className="text-xs bg-white/20 px-2 py-1 rounded">
                {selectedElement.id}
              </span>
            </div>
            <span className={`transform transition-transform ${openSections.effectLibrary ? 'rotate-180' : ''}`}>
              ▼
            </span>
          </button>

          {openSections.effectLibrary && (
            <div className="p-4">
              <EffectPanel
                elementId={selectedElement.id}
                onAddEffect={onAddEffect}
                effectEngine={effectEngine}
              />
            </div>
          )}
        </div>
      )}

      {/* Group Effects Section */}
      {currentSequence && (
        <div className="bg-gray-700 rounded-lg overflow-hidden">
          <button
            onClick={() => toggleSection('groupEffects')}
            className="w-full px-4 py-3 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-semibold flex items-center justify-between transition-all"
          >
            <div className="flex items-center gap-2">
              <span>🏗️</span>
              <span>Gruppen-Effekte</span>
              <span className="text-xs bg-white/20 px-2 py-1 rounded">
                {currentSequence.name}
              </span>
            </div>
            <span className={`transform transition-transform ${openSections.groupEffects ? 'rotate-180' : ''}`}>
              ▼
            </span>
          </button>

          {openSections.groupEffects && (
            <div className="p-4">
              <GroupEffectsPanel
                groupEngine={groupEngine}
                onAddGroupEffectToSequence={onAddGroupEffectToSequence}
                currentSequence={currentSequence}
                effectEngine={effectEngine}
              />
            </div>
          )}
        </div>
      )}

      {/* Helper Text */}
      {!selectedElement && !currentSequence && (
        <div className="bg-gray-700 rounded-lg p-6 text-center">
          <div className="text-gray-400 mb-2">
            <span className="text-2xl">🎭</span>
          </div>
          <p className="text-gray-300 text-sm">
            Wählen Sie ein Element oder erstellen Sie eine Sequenz, um Effekte zu verwenden
          </p>
        </div>
      )}
    </div>
  );
};

// ==================== GROUP EFFECTS PANEL ====================

// Enhanced parameter definitions for effect types
const getEffectParameterDefinitions = (effectType) => {
  switch (effectType) {
    case 'pulse':
      return {
        color: { type: 'color', default: '#ff6b6b', label: 'Pulse Color' },
        intensity: { type: 'range', default: 0.8, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        speed: { type: 'range', default: 1.5, min: 0.1, max: 5.0, step: 0.1, label: 'Speed' },
        pulseWidth: { type: 'range', default: 0.5, min: 0.1, max: 1.0, step: 0.05, label: 'Pulse Width' },
        brightness: { type: 'range', default: 1.0, min: 0.2, max: 3.0, step: 0.1, label: 'Brightness' }
      };
    case 'colorFade':
      return {
        from: { type: 'color', default: '#ff0000', label: 'Start Color' },
        to: { type: 'color', default: '#0000ff', label: 'End Color' },
        speed: { type: 'range', default: 1.0, min: 0.1, max: 5.0, step: 0.1, label: 'Fade Speed' },
        easing: { type: 'select', default: 'easeInOut', options: ['linear', 'easeIn', 'easeOut', 'easeInOut'], label: 'Easing' }
      };
    case 'rainbow':
      return {
        speed: { type: 'range', default: 1.0, min: 0.1, max: 5.0, step: 0.1, label: 'Speed' },
        intensity: { type: 'range', default: 0.8, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        cycles: { type: 'range', default: 2, min: 1, max: 10, step: 1, label: 'Cycles' },
        saturation: { type: 'range', default: 100, min: 20, max: 100, step: 5, label: 'Saturation %' },
        lightness: { type: 'range', default: 50, min: 20, max: 80, step: 5, label: 'Lightness %' }
      };
    case 'strobe':
      return {
        color: { type: 'color', default: '#ffffff', label: 'Strobe Color' },
        frequency: { type: 'range', default: 8, min: 1, max: 30, step: 1, label: 'Frequency' },
        intensity: { type: 'range', default: 1.0, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        dutyCycle: { type: 'range', default: 0.5, min: 0.1, max: 0.9, step: 0.05, label: 'Duty Cycle' }
      };
    case 'windowGlow':
      return {
        glowColor: { type: 'color', default: '#FFE4B5', label: 'Glow Color' },
        intensity: { type: 'range', default: 0.9, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        spreadRadius: { type: 'range', default: 8, min: 2, max: 30, step: 1, label: 'Glow Radius' },
        innerGlow: { type: 'boolean', default: true, label: 'Inner Glow' },
        pulseRate: { type: 'range', default: 1.0, min: 0.1, max: 3.0, step: 0.1, label: 'Pulse Rate' }
      };
    case 'shadowCast':
      return {
        shadowLength: { type: 'range', default: 50, min: 10, max: 200, step: 5, label: 'Shadow Length' },
        shadowAngle: { type: 'range', default: 45, min: 0, max: 360, step: 15, label: 'Shadow Angle' },
        shadowOpacity: { type: 'range', default: 0.6, min: 0.1, max: 1.0, step: 0.05, label: 'Shadow Opacity' },
        rotation: { type: 'range', default: 360, min: 0, max: 720, step: 30, label: 'Rotation Degrees' },
        shadowColor: { type: 'color', default: '#000000', label: 'Shadow Color' }
      };
    case 'scale':
      return {
        scale: { type: 'range', default: 1.2, min: 0.5, max: 3.0, step: 0.1, label: 'Scale Factor' },
        speed: { type: 'range', default: 1.0, min: 0.1, max: 5.0, step: 0.1, label: 'Animation Speed' },
        easing: { type: 'select', default: 'easeInOut', options: ['linear', 'easeIn', 'easeOut', 'easeInOut'], label: 'Easing' }
      };
    case 'rotate':
      return {
        degrees: { type: 'range', default: 360, min: 45, max: 1080, step: 45, label: 'Rotation Degrees' },
        speed: { type: 'range', default: 1.0, min: 0.1, max: 5.0, step: 0.1, label: 'Rotation Speed' },
        direction: { type: 'select', default: 'clockwise', options: ['clockwise', 'counterclockwise'], label: 'Direction' }
      };
    case 'breathe':
      return {
        breathRate: { type: 'range', default: 0.2, min: 0.05, max: 1.0, step: 0.05, label: 'Breath Rate' },
        depth: { type: 'range', default: 0.3, min: 0.1, max: 0.8, step: 0.05, label: 'Breath Depth' },
        asymmetry: { type: 'range', default: 0.5, min: 0.1, max: 0.9, step: 0.05, label: 'Asymmetry' },
        minOpacity: { type: 'range', default: 0.3, min: 0.0, max: 0.8, step: 0.05, label: 'Min Opacity' }
      };
    case 'warmWindowLight':
      return {
        warmColor: { type: 'color', default: '#FFA500', label: 'Warm Color' },
        intensity: { type: 'range', default: 0.7, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        flicker: { type: 'range', default: 0.1, min: 0.0, max: 0.5, step: 0.05, label: 'Flicker Amount' },
        opacity: { type: 'range', default: 0.8, min: 0.2, max: 1.0, step: 0.05, label: 'Base Opacity' },
        flickerSpeed: { type: 'range', default: 3.0, min: 0.5, max: 10.0, step: 0.5, label: 'Flicker Speed' }
      };
    case 'slide':
      return {
        x: { type: 'range', default: 0, min: -100, max: 100, step: 5, label: 'X Movement' },
        y: { type: 'range', default: 0, min: -100, max: 100, step: 5, label: 'Y Movement' },
        speed: { type: 'range', default: 1.0, min: 0.1, max: 5.0, step: 0.1, label: 'Movement Speed' },
        easing: { type: 'select', default: 'easeInOut', options: ['linear', 'easeIn', 'easeOut', 'easeInOut'], label: 'Easing' }
      };
    case 'architecturalGlow':
      return {
        balkenColor: { type: 'color', default: '#8B4513', label: 'Balken Color' },
        gefachColor: { type: 'color', default: '#F5E6D3', label: 'Gefach Color' },
        fensterColor: { type: 'color', default: '#FFE4B5', label: 'Fenster Color' },
        intensity: { type: 'range', default: 0.8, min: 0.1, max: 2.0, step: 0.1, label: 'Overall Intensity' },
        pulseRate: { type: 'range', default: 1.0, min: 0.1, max: 3.0, step: 0.1, label: 'Pulse Rate' }
      };
    case 'mirrorGlow':
      return {
        primaryColor: { type: 'color', default: '#00FF88', label: 'Primary Color' },
        secondaryColor: { type: 'color', default: '#FF0088', label: 'Secondary Color' },
        intensity: { type: 'range', default: 0.7, min: 0.1, max: 2.0, step: 0.1, label: 'Intensity' },
        symmetryAxis: { type: 'select', default: 'vertical', options: ['vertical', 'horizontal'], label: 'Symmetry Axis' },
        glowRadius: { type: 'range', default: 12, min: 4, max: 30, step: 2, label: 'Glow Radius' }
      };
    default:
      return {};
  }
};

// Helper function to get default parameters for effect types (backward compatibility)
const getDefaultEffectParameters = (effectType) => {
  const definitions = getEffectParameterDefinitions(effectType);
  const defaults = {};
  Object.entries(definitions).forEach(([key, def]) => {
    defaults[key] = def.default;
  });
  return defaults;
};

const GroupEffectsPanel = ({ groupEngine, onAddGroupEffectToSequence, currentSequence, effectEngine }) => {
  const [selectedGroup, setSelectedGroup] = useState('gefache');
  const [selectedPattern, setSelectedPattern] = useState('alternating');
  const [selectedEffect, setSelectedEffect] = useState('pulse');
  const [timing, setTiming] = useState({
    duration: 4000,
    elementDuration: 1000,
    beatSync: true,
    quantization: 'beat'
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [effectParameters, setEffectParameters] = useState({});
  const [currentPreviewEffect, setCurrentPreviewEffect] = useState(null);

  // Update parameters when effect type changes
  useEffect(() => {
    const defaultParams = getDefaultEffectParameters(selectedEffect);
    setEffectParameters(defaultParams);
  }, [selectedEffect]);

  const handleParameterChange = (paramKey, value) => {
    setEffectParameters(prev => {
      const newParams = {
        ...prev,
        [paramKey]: value
      };

      // If there's a running preview effect, update its parameters in real-time
      if (currentPreviewEffect && effectEngine?.updateAnimationParameters) {
        // Update all animation IDs associated with this preview effect
        currentPreviewEffect.animationIds?.forEach(animationId => {
          effectEngine.updateAnimationParameters(animationId, newParams);
        });
      }

      return newParams;
    });
  };

  const handleApplyLiveEffect = () => {
    // Stop any existing preview effect
    if (currentPreviewEffect && effectEngine?.stopAnimation) {
      currentPreviewEffect.animationIds?.forEach(animationId => {
        effectEngine.stopAnimation(animationId);
      });
    }

    // Create a new preview effect
    const previewEffect = {
      name: `Preview ${effectTypes[selectedEffect]?.name || selectedEffect}`,
      targetGroup: selectedGroup,
      pattern: selectedPattern,
      effect: {
        type: selectedEffect,
        parameters: effectParameters
      },
      timing: timing
    };

    // Apply the effect and store the result for real-time updates
    const result = onAddGroupEffectToSequence(previewEffect);
    if (result?.animationIds) {
      setCurrentPreviewEffect({
        ...previewEffect,
        animationIds: result.animationIds
      });
    }
  };

  const groupLibrary = groupEngine ? groupEngine.getGroupEffectLibrary() : {};
  const groupPatterns = groupEngine ? groupEngine.getGroupPatterns() : {};

  // Available effect types for custom group effects
  const effectTypes = {
    pulse: { name: 'Pulse', icon: '💓', color: '#ff6b6b' },
    colorFade: { name: 'Color Fade', icon: '🌈', color: '#4ecdc4' },
    rainbow: { name: 'Rainbow', icon: '🌈', color: '#45b7d1' },
    strobe: { name: 'Strobe', icon: '⚡', color: '#f9ca24' },
    breathe: { name: 'Breathe', icon: '🫁', color: '#6c5ce7' },
    windowGlow: { name: 'Window Glow', icon: '🪟', color: '#fdcb6e' },
    warmWindowLight: { name: 'Warm Light', icon: '🕯️', color: '#e17055' }
  };

  if (!groupEngine) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="text-center text-gray-400">
          <div className="text-2xl mb-2">📁</div>
          <div className="text-sm">Lade SVG um Gruppen zu analysieren</div>
        </div>
      </div>
    );
  }

  if (!currentSequence) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-bold text-white mb-3 flex items-center gap-2">
          <span>🎭</span> Group Effects
        </h3>
        <div className="text-center text-gray-400">
          <div className="text-2xl mb-2">🎬</div>
          <div className="text-sm">Wähle eine Sequenz um Gruppeneffekte hinzuzufügen</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-bold text-white flex items-center gap-2">
          <span>🎭</span> Group Effects für Sequenz
        </h3>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">
            {groupEngine?.groups[selectedGroup]?.length || 0} Elements
          </span>
          <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
            {currentSequence.name}
          </span>
        </div>
      </div>

      {/* Group Overview */}
      <div className="grid grid-cols-2 gap-2 text-xs">
        {Object.entries(groupEngine.groups).filter(([_, elements]) => elements.length > 0).map(([groupName, elements]) => (
          <div
            key={groupName}
            className={`p-2 rounded border cursor-pointer transition-all ${
              selectedGroup === groupName
                ? 'bg-blue-600 border-blue-400'
                : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
            }`}
            onClick={() => setSelectedGroup(groupName)}
          >
            <div className="font-medium">{groupName}</div>
            <div className="text-gray-300">{elements.length} elements</div>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-300">🚀 Quick Presets</label>
        <div className="grid grid-cols-1 gap-2">
          {Object.entries(groupLibrary).slice(0, 4).map(([key, preset]) => (
            <button
              key={key}
              onClick={() => onAddGroupEffectToSequence(preset)}
              className="w-full px-4 py-3 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 rounded-lg text-left transition-all transform hover:scale-105"
            >
              <div className="font-bold text-sm">➕ {preset.name}</div>
              <div className="text-xs text-gray-300">
                {preset.targetGroup} • {preset.pattern} • {groupEngine.groups[preset.targetGroup]?.length || 0} elements
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="border-t border-gray-700 pt-4">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-gray-300">🎨 Custom Effect Builder</label>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-xs text-blue-400 hover:text-blue-300"
          >
            {showAdvanced ? 'Simple' : 'Advanced'}
          </button>
        </div>

        {/* Effect Type Selection */}
        <div className="space-y-2 mb-4">
          <label className="text-xs text-gray-400">Effect Type</label>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(effectTypes).map(([key, effect]) => (
              <button
                key={key}
                onClick={() => setSelectedEffect(key)}
                className={`p-2 rounded text-xs transition-all ${
                  selectedEffect === key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                <div>{effect.icon} {effect.name}</div>
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <label className="text-xs text-gray-400">Activation Pattern</label>
          <select
            value={selectedPattern}
            onChange={(e) => setSelectedPattern(e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 rounded-lg text-sm"
          >
            {Object.entries(groupPatterns).map(([key, pattern]) => (
              <option key={key} value={key}>
                {pattern.name} {key.includes('Chase') ? '🔄' : ''}
              </option>
            ))}
          </select>
          <div className="text-xs text-gray-500">
            {groupPatterns[selectedPattern]?.description}
            {selectedPattern.includes('Chase') && (
              <span className="text-yellow-400 ml-2">🔄 Chase: Previous effect stops</span>
            )}
          </div>
        </div>

        {/* Effect Parameters Panel */}
        <EffectParametersPanel
          effectType={selectedEffect}
          parameters={effectParameters}
          onParameterChange={handleParameterChange}
          onApplyEffect={handleApplyLiveEffect}
        />

        {showAdvanced && (
          <TimingControls timing={timing} onTimingChange={setTiming} />
        )}

        <button
          onClick={() => {
            const customEffect = {
              name: `${effectTypes[selectedEffect]?.name || 'Custom'} ${selectedGroup}`,
              targetGroup: selectedGroup,
              pattern: selectedPattern,
              effect: {
                type: selectedEffect,
                parameters: effectParameters
              },
              timing: timing
            };
            onAddGroupEffectToSequence(customEffect);
          }}
          className="w-full py-3 bg-green-600 hover:bg-green-700 rounded-lg font-bold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
        >
          <span>➕</span>
          Add to Sequence ({groupEngine?.groups[selectedGroup]?.length || 0} Elements)
        </button>

        {/* Preview Section */}
        <div className="mt-4 p-3 bg-gray-700 rounded-lg">
          <div className="text-xs text-gray-400 mb-2">Preview: {selectedPattern} pattern on {selectedGroup}</div>
          <div className="text-xs text-gray-300">
            Will add {effectTypes[selectedEffect]?.name} effect to {groupEngine?.groups[selectedGroup]?.length || 0} elements
            {timing.beatSync ? ` synced to ${timing.quantization}` : ` with ${timing.elementDuration}ms duration`}
            {selectedPattern.includes('Chase') && (
              <div className="text-yellow-400 mt-1">
                🔄 Chase mode: Effects loop infinitely, only one active at a time
              </div>
            )}
          </div>
        </div>
      </div>

      {groupEngine && (
        <GroupPreview 
          group={selectedGroup}
          elements={groupEngine.groups[selectedGroup]}
          pattern={selectedPattern}
        />
      )}
    </div>
  );
};

// Enhanced Parameter Control Component
const ParameterControl = ({ paramKey, paramDef, value, onChange }) => {
  const handleChange = (newValue) => {
    onChange(paramKey, newValue);
  };

  switch (paramDef.type) {
    case 'range':
      return (
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-gray-400">{paramDef.label}</span>
            <span className="text-gray-300 font-mono">
              {typeof value === 'number' ? value.toFixed(paramDef.step < 1 ? 2 : 0) : value}
              {paramDef.label.includes('%') ? '%' : ''}
            </span>
          </div>
          <input
            type="range"
            min={paramDef.min}
            max={paramDef.max}
            step={paramDef.step}
            value={value || paramDef.default}
            onChange={(e) => handleChange(parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      );

    case 'color':
      return (
        <div className="space-y-1">
          <label className="text-xs text-gray-400">{paramDef.label}</label>
          <div className="flex gap-2">
            <input
              type="color"
              value={value || paramDef.default}
              onChange={(e) => handleChange(e.target.value)}
              className="w-full h-8 rounded border-0 cursor-pointer"
            />
            <input
              type="text"
              value={value || paramDef.default}
              onChange={(e) => handleChange(e.target.value)}
              className="w-20 px-2 py-1 bg-gray-600 rounded text-xs text-white"
              placeholder="#FFFFFF"
            />
          </div>
        </div>
      );

    case 'select':
      return (
        <div className="space-y-1">
          <label className="text-xs text-gray-400">{paramDef.label}</label>
          <select
            value={value || paramDef.default}
            onChange={(e) => handleChange(e.target.value)}
            className="w-full px-2 py-1 bg-gray-600 rounded text-xs text-white"
          >
            {paramDef.options.map(option => (
              <option key={option} value={option}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </option>
            ))}
          </select>
        </div>
      );

    case 'boolean':
      return (
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={value !== undefined ? value : paramDef.default}
            onChange={(e) => handleChange(e.target.checked)}
            className="rounded"
          />
          <label className="text-xs text-gray-300">{paramDef.label}</label>
        </div>
      );

    default:
      return null;
  }
};

// Advanced Effect Parameters Panel
const EffectParametersPanel = ({ effectType, parameters, onParameterChange, onApplyEffect }) => {
  const [savedPresets, setSavedPresets] = useState({});
  const [presetName, setPresetName] = useState('');
  const [showPresetControls, setShowPresetControls] = useState(false);

  const paramDefinitions = getEffectParameterDefinitions(effectType);

  // Load saved presets from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem(`effectPresets_${effectType}`);
    if (saved) {
      try {
        setSavedPresets(JSON.parse(saved));
      } catch (error) {
        console.warn('Failed to load saved presets:', error);
      }
    }
  }, [effectType]);

  const savePreset = () => {
    if (!presetName.trim()) return;

    const newPresets = {
      ...savedPresets,
      [presetName]: { ...parameters }
    };

    setSavedPresets(newPresets);
    localStorage.setItem(`effectPresets_${effectType}`, JSON.stringify(newPresets));
    setPresetName('');
    setShowPresetControls(false);
  };

  const loadPreset = (name) => {
    const preset = savedPresets[name];
    if (preset) {
      Object.entries(preset).forEach(([key, value]) => {
        onParameterChange(key, value);
      });
    }
  };

  const deletePreset = (name) => {
    const newPresets = { ...savedPresets };
    delete newPresets[name];
    setSavedPresets(newPresets);
    localStorage.setItem(`effectPresets_${effectType}`, JSON.stringify(newPresets));
  };

  if (!effectType || Object.keys(paramDefinitions).length === 0) {
    return null;
  }

  return (
    <div className="space-y-3 mb-4 p-3 bg-gray-700 rounded-lg">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-300">Effect Parameters</label>
        <div className="flex gap-2">
          <button
            onClick={() => setShowPresetControls(!showPresetControls)}
            className="px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded transition-colors"
          >
            💾 Presets
          </button>
          <button
            onClick={onApplyEffect}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
          >
            Apply Live
          </button>
        </div>
      </div>

      {/* Preset Controls */}
      {showPresetControls && (
        <div className="space-y-2 p-2 bg-gray-600 rounded">
          <div className="flex gap-2">
            <input
              type="text"
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
              placeholder="Preset name..."
              className="flex-1 px-2 py-1 bg-gray-700 text-white text-xs rounded"
            />
            <button
              onClick={savePreset}
              disabled={!presetName.trim()}
              className="px-2 py-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white text-xs rounded"
            >
              Save
            </button>
          </div>

          {Object.keys(savedPresets).length > 0 && (
            <div className="space-y-1">
              <div className="text-xs text-gray-300">Saved Presets:</div>
              {Object.keys(savedPresets).map(name => (
                <div key={name} className="flex gap-2 items-center">
                  <button
                    onClick={() => loadPreset(name)}
                    className="flex-1 px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded text-left"
                  >
                    {name}
                  </button>
                  <button
                    onClick={() => deletePreset(name)}
                    className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 gap-3">
        {Object.entries(paramDefinitions).map(([key, def]) => (
          <ParameterControl
            key={key}
            paramKey={key}
            paramDef={def}
            value={parameters[key]}
            onChange={onParameterChange}
          />
        ))}
      </div>
    </div>
  );
};

const TimingControls = ({ timing, onTimingChange }) => {
  return (
    <div className="space-y-3 mb-4 p-3 bg-gray-700 rounded-lg">
      <label className="text-sm font-medium text-gray-300">Timing Settings</label>
      
      <div className="space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-gray-400">Total Duration</span>
          <span className="text-gray-300">{timing.duration}ms</span>
        </div>
        <input
          type="range"
          min="500"
          max="16000"
          step="250"
          value={timing.duration}
          onChange={(e) => onTimingChange({
            ...timing,
            duration: parseInt(e.target.value)
          })}
          className="w-full"
        />
      </div>

      <div className="space-y-1">
        <div className="flex justify-between text-xs">
          <span className="text-gray-400">Element Duration</span>
          <span className="text-gray-300">{timing.elementDuration}ms</span>
        </div>
        <input
          type="range"
          min="50"
          max="10000"
          step="50"
          value={timing.elementDuration}
          onChange={(e) => onTimingChange({
            ...timing,
            elementDuration: parseInt(e.target.value)
          })}
          className="w-full"
        />
      </div>

      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-400">Beat Sync</span>
        <button
          onClick={() => onTimingChange({
            ...timing,
            beatSync: !timing.beatSync
          })}
          className={`px-3 py-1 rounded text-xs font-bold ${
            timing.beatSync ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
          }`}
        >
          {timing.beatSync ? 'ON' : 'OFF'}
        </button>
      </div>

      {timing.beatSync && (
        <div className="space-y-2">
          <label className="text-xs text-gray-400">Quantization</label>
          <div className="grid grid-cols-4 gap-1">
            {['beat', 'bar', '2-bar', '4-bar'].map(option => (
              <button
                key={option}
                onClick={() => onTimingChange({
                  ...timing,
                  quantization: option
                })}
                className={`py-1 px-2 rounded text-xs font-bold ${
                  timing.quantization === option
                    ? 'bg-yellow-600 text-white'
                    : 'bg-gray-600 hover:bg-gray-500 text-gray-300'
                }`}
              >
                {option.toUpperCase()}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const GroupPreview = ({ group, elements, pattern }) => {
  const getPatternColor = (pattern, index) => {
    switch (pattern) {
      case 'alternating':
        return index % 2 === 0 ? 'bg-blue-600 text-white' : 'bg-red-600 text-white';
      case 'sequence':
        return 'bg-gradient-to-r from-green-600 to-green-400 text-white';
      case 'wave':
        return 'bg-purple-600 text-white';
      default:
        return 'bg-gray-600 text-gray-300';
    }
  };

  return (
    <div className="bg-gray-700 rounded-lg p-3 space-y-2">
      <div className="text-sm font-medium text-gray-300">Preview</div>
      <div className="grid grid-cols-4 gap-1">
        {elements?.slice(0, 8).map((element, index) => (
          <div
            key={element.id}
            className={`h-6 rounded text-xs flex items-center justify-center font-bold ${
              getPatternColor(pattern, index)
            }`}
          >
            {element.id}
          </div>
        ))}
      </div>
      <div className="text-xs text-gray-500">
        Pattern: {pattern} • {elements?.length || 0} elements
      </div>
    </div>
  );
};

// ==================== ACTIVE GROUP EFFECTS PANEL ====================

const ActiveGroupEffectsPanel = ({ activeEffects, onStopEffect, onStopAll }) => {
  if (activeEffects.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-bold text-white mb-3 flex items-center gap-2">
          <span>🎬</span> Active Group Effects
        </h3>
        <div className="text-center text-gray-400">
          <div className="text-2xl mb-2">💤</div>
          <div className="text-sm">No active group effects</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-bold text-white flex items-center gap-2">
          <span>🎬</span> Active Group Effects
        </h3>
        <button
          onClick={onStopAll}
          className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
        >
          Stop All
        </button>
      </div>

      <div className="space-y-2">
        {activeEffects.map((effect) => (
          <div key={effect.id} className="bg-gray-700 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="font-medium text-white">{effect.name}</div>
              <button
                onClick={() => onStopEffect(effect.id)}
                className="px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
              >
                Stop
              </button>
            </div>
            <div className="text-xs text-gray-300 space-y-1">
              <div>Target: {effect.targetGroup} ({effect.elementCount} elements)</div>
              <div>Running: {Math.round((Date.now() - effect.startTime) / 1000)}s</div>
              <div>Animations: {effect.animationIds.length}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// ==================== ENHANCED EFFECT PANEL ====================

const EffectPanel = ({ elementId, onAddEffect, effectEngine }) => {
  const [selectedCategory, setSelectedCategory] = useState('color');
  const [selectedEffect, setSelectedEffect] = useState('pulse');
  const [effectParameters, setEffectParameters] = useState({});
  const [currentPreviewEffect, setCurrentPreviewEffect] = useState(null);
  const [customColors, setCustomColors] = useState({
    from: '#ff0000',
    to: '#0000ff',
    single: '#ff0000'
  });

  // Update parameters when effect type changes
  useEffect(() => {
    const defaultParams = getDefaultEffectParameters(selectedEffect);
    setEffectParameters(defaultParams);
  }, [selectedEffect]);

  const handleParameterChange = (paramKey, value) => {
    setEffectParameters(prev => {
      const newParams = {
        ...prev,
        [paramKey]: value
      };

      // If there's a running preview effect, update its parameters in real-time
      if (currentPreviewEffect && effectEngine?.updateAnimationParameters) {
        effectEngine.updateAnimationParameters(currentPreviewEffect.animationId, newParams);
      }

      return newParams;
    });
  };

  const handleApplyLiveEffect = () => {
    // Stop any existing preview effect
    if (currentPreviewEffect && effectEngine?.stopAnimation) {
      effectEngine.stopAnimation(currentPreviewEffect.animationId);
    }

    // Apply the effect with current parameters
    const animationId = effectEngine?.startAnimation(elementId, {
      type: selectedEffect,
      ...effectParameters
    }, 3000);

    if (animationId) {
      setCurrentPreviewEffect({
        animationId,
        effectType: selectedEffect,
        parameters: effectParameters
      });
    }
  };

  const colorPalettes = {
    warm: ['#ff4444', '#ff8844', '#ffaa44', '#ffcc44', '#ffff44'],
    cool: ['#4444ff', '#4488ff', '#44aaff', '#44ccff', '#44ffff'],
    nature: ['#44ff44', '#88ff44', '#aaff44', '#ccff44', '#ffff44'],
    sunset: ['#ff6b35', '#f7931e', '#ffd23f', '#06ffa5', '#118ab2'],
    neon: ['#ff0080', '#ff8000', '#ffff00', '#80ff00', '#00ff80'],
    pastel: ['#ffb3ba', '#ffdfba', '#ffffba', '#baffc9', '#bae1ff']
  };

  const applyEffectWithColors = (effectType, baseParams, colors = {}) => {
    const params = { ...baseParams, ...colors };
    onAddEffect(effectType, params);
  };

  // Enhanced preset combinations for single object effects
  const singleObjectPresets = {
    'pulse-intense': {
      type: 'pulse',
      name: '🔥 Intensives Pulsieren',
      parameters: { color: '#ff4444', intensity: 1.4, speed: 2.2, pulseWidth: 0.6, brightness: 1.3 }
    },
    'pulse-gentle': {
      type: 'pulse',
      name: '💫 Sanftes Pulsieren',
      parameters: { color: '#88ccff', intensity: 0.6, speed: 0.8, pulseWidth: 0.8, brightness: 1.1 }
    },
    'rainbow-vivid': {
      type: 'rainbow',
      name: '🌈 Lebendiger Regenbogen',
      parameters: { speed: 1.8, intensity: 1.2, cycles: 3, saturation: 95, lightness: 65 }
    },
    'rainbow-subtle': {
      type: 'rainbow',
      name: '🎨 Subtiler Regenbogen',
      parameters: { speed: 0.6, intensity: 0.7, cycles: 1, saturation: 70, lightness: 45 }
    },
    'strobe-party': {
      type: 'strobe',
      name: '⚡ Party Stroboskop',
      parameters: { color: '#ffffff', frequency: 15, intensity: 1.5, dutyCycle: 0.25 }
    },
    'strobe-warning': {
      type: 'strobe',
      name: '🚨 Warnung Stroboskop',
      parameters: { color: '#ff0000', frequency: 4, intensity: 1.2, dutyCycle: 0.5 }
    },
    'window-warm': {
      type: 'windowGlow',
      name: '🏠 Warmes Fensterglühen',
      parameters: { glowColor: '#FFE4B5', intensity: 1.1, spreadRadius: 12, innerGlow: true, pulseRate: 0.5 }
    },
    'window-cool': {
      type: 'windowGlow',
      name: '❄️ Kühles Fensterglühen',
      parameters: { glowColor: '#87CEEB', intensity: 0.9, spreadRadius: 8, innerGlow: true, pulseRate: 1.2 }
    },
    'breathe-calm': {
      type: 'breathe',
      name: '🫁 Ruhiges Atmen',
      parameters: { breathRate: 0.12, depth: 0.3, asymmetry: 0.7, minOpacity: 0.5 }
    },
    'breathe-excited': {
      type: 'breathe',
      name: '💨 Aufgeregtes Atmen',
      parameters: { breathRate: 0.4, depth: 0.6, asymmetry: 0.4, minOpacity: 0.2 }
    }
  };

  const effectCategories = {
    color: {
      name: 'Farben',
      icon: <Palette size={16} />,
      effects: ['pulse', 'colorFade', 'rainbow', 'strobe'],
      presets: ['pulse-intense', 'pulse-gentle', 'rainbow-vivid', 'rainbow-subtle', 'strobe-party', 'strobe-warning']
    },
    transform: {
      name: 'Bewegung',
      icon: <Zap size={16} />,
      effects: ['scale', 'rotate', 'slide'],
      presets: []
    },
    atmospheric: {
      name: 'Atmosphärisch',
      icon: <Layers size={16} />,
      effects: ['breathe', 'shadowCast'],
      presets: ['breathe-calm', 'breathe-excited']
    },
    fenster: {
      name: 'Fenster',
      icon: <Monitor size={16} />,
      effects: ['windowGlow', 'warmWindowLight'],
      presets: ['window-warm', 'window-cool']
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-white font-semibold mb-3">Effekte für {elementId}</h3>

      <div className="flex gap-2 mb-4">
        {Object.entries(effectCategories).map(([key, category]) => (
          <button
            key={key}
            onClick={() => setSelectedCategory(key)}
            className={`flex items-center gap-1 px-3 py-2 rounded transition-colors ${
              selectedCategory === key
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {category.icon}
            <span className="text-sm">{category.name}</span>
          </button>
        ))}
      </div>

      {/* Effect Type Selection */}
      <div className="mb-4">
        <label className="text-sm font-medium text-gray-300 mb-2 block">Effekt-Typ</label>
        <select
          value={selectedEffect}
          onChange={(e) => setSelectedEffect(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg"
        >
          {effectCategories[selectedCategory].effects.map(effectType => (
            <option key={effectType} value={effectType}>
              {effectTypes[effectType]?.name || effectType}
            </option>
          ))}
        </select>
      </div>

      {/* Enhanced Effect Parameters Panel */}
      <EffectParametersPanel
        effectType={selectedEffect}
        parameters={effectParameters}
        onParameterChange={handleParameterChange}
        onApplyEffect={handleApplyLiveEffect}
      />

      {/* Enhanced Presets Section */}
      {effectCategories[selectedCategory].presets && effectCategories[selectedCategory].presets.length > 0 && (
        <div className="mb-4">
          <label className="text-sm font-medium text-gray-300 mb-2 block">✨ Vorgefertigte Presets</label>
          <div className="grid grid-cols-1 gap-2">
            {effectCategories[selectedCategory].presets.map(presetKey => {
              const preset = singleObjectPresets[presetKey];
              return (
                <button
                  key={presetKey}
                  onClick={() => {
                    setSelectedEffect(preset.type);
                    setEffectParameters(preset.parameters);
                    // Apply immediately for preview
                    setTimeout(() => handleApplyLiveEffect(), 100);
                  }}
                  className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg transition-all transform hover:scale-105"
                >
                  <span className="text-sm font-medium text-white">{preset.name}</span>
                  <span className="text-xs text-purple-200">Anwenden</span>
                </button>
              );
            })}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-3">
        <button
          onClick={() => {
            onAddEffect(selectedEffect, effectParameters);
          }}
          className="w-full py-3 bg-green-600 hover:bg-green-700 rounded-lg font-bold transition-all transform hover:scale-105 flex items-center justify-center gap-2"
        >
          <span>➕</span>
          Effekt zu Sequenz hinzufügen
        </button>
      </div>
    </div>
  );
};

// ==================== MAIN APPLICATION ====================

const FachwerkwandApp = () => {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const [expandedSequences, setExpandedSequences] = useState(new Set());

  const svgRef = useRef();
  const fileInputRef = useRef();
  const autoSaveTimer = useRef();

  // Initialize Beat Engine
  const [beatEngine] = useState(() => new BeatEngine(dispatch));
  
  // Initialize Group Engine
  const [groupEngine, setGroupEngine] = useState(null);
  const [activeGroupEffects, setActiveGroupEffects] = useState([]);

  const effectEngine = useEffectEngine(
    state.player.deckA,
    state.player.deckB,
    state.player.crossfadeValue,
    state.player.masterVolume,
    beatEngine
  );

  // Demo SVG Content with Fenster
  const demoSVG = `
    <svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
        <filter id="windowGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      <!-- Balken -->
      <g class="balken" id="B1">
        <rect x="50" y="50" width="700" height="20" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      <g class="balken" id="B2">
        <rect x="50" y="250" width="700" height="20" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      <g class="balken" id="B3">
        <rect x="50" y="450" width="700" height="20" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      <g class="balken" id="B4">
        <rect x="50" y="50" width="20" height="420" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      <g class="balken" id="B5">
        <rect x="730" y="50" width="20" height="420" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      <g class="balken" id="B6">
        <rect x="350" y="50" width="20" height="420" fill="#8B4513" stroke="#654321" stroke-width="2"/>
      </g>
      
      <!-- Gefache -->
      <g class="gefach" id="G1">
        <rect x="70" y="70" width="280" height="180" fill="#F5E6D3" stroke="#D2B48C" stroke-width="1"/>
      </g>
      <g class="gefach" id="G2">
        <rect x="370" y="70" width="360" height="180" fill="#F5E6D3" stroke="#D2B48C" stroke-width="1"/>
      </g>
      <g class="gefach" id="G3">
        <rect x="70" y="270" width="280" height="180" fill="#F5E6D3" stroke="#D2B48C" stroke-width="1"/>
      </g>
      <g class="gefach" id="G4">
        <rect x="370" y="270" width="360" height="180" fill="#F5E6D3" stroke="#D2B48C" stroke-width="1"/>
      </g>
      
      <!-- Fenster in Gefach 1 -->
      <g class="fenster" id="F1">
        <rect x="120" y="120" width="60" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="150" y1="120" x2="150" y2="200" stroke="#4682B4" stroke-width="1"/>
        <line x1="120" y1="160" x2="180" y2="160" stroke="#4682B4" stroke-width="1"/>
      </g>
      <g class="fenster" id="F2">
        <rect x="220" y="120" width="60" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="250" y1="120" x2="250" y2="200" stroke="#4682B4" stroke-width="1"/>
        <line x1="220" y1="160" x2="280" y2="160" stroke="#4682B4" stroke-width="1"/>
      </g>
      
      <!-- Fenster in Gefach 2 -->
      <g class="fenster" id="F3">
        <rect x="450" y="120" width="80" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="490" y1="120" x2="490" y2="200" stroke="#4682B4" stroke-width="1"/>
        <line x1="450" y1="160" x2="530" y2="160" stroke="#4682B4" stroke-width="1"/>
      </g>
      <g class="fenster" id="F4">
        <rect x="580" y="120" width="80" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="620" y1="120" x2="620" y2="200" stroke="#4682B4" stroke-width="1"/>
        <line x1="580" y1="160" x2="660" y2="160" stroke="#4682B4" stroke-width="1"/>
      </g>
      
      <!-- Fenster in Gefach 3 -->
      <g class="fenster" id="F5">
        <rect x="120" y="320" width="60" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="150" y1="320" x2="150" y2="400" stroke="#4682B4" stroke-width="1"/>
        <line x1="120" y1="360" x2="180" y2="360" stroke="#4682B4" stroke-width="1"/>
      </g>
      <g class="fenster" id="F6">
        <rect x="220" y="320" width="60" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="250" y1="320" x2="250" y2="400" stroke="#4682B4" stroke-width="1"/>
        <line x1="220" y1="360" x2="280" y2="360" stroke="#4682B4" stroke-width="1"/>
      </g>
      
      <!-- Fenster in Gefach 4 -->
      <g class="fenster" id="F7">
        <rect x="450" y="320" width="80" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="490" y1="320" x2="490" y2="400" stroke="#4682B4" stroke-width="1"/>
        <line x1="450" y1="360" x2="530" y2="360" stroke="#4682B4" stroke-width="1"/>
      </g>
      <g class="fenster" id="F8">
        <rect x="580" y="320" width="80" height="80" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
        <line x1="620" y1="320" x2="620" y2="400" stroke="#4682B4" stroke-width="1"/>
        <line x1="580" y1="360" x2="660" y2="360" stroke="#4682B4" stroke-width="1"/>
      </g>
    </svg>
  `;

  // Initialize demo content
  useEffect(() => {
    if (!state.project.svgContent) {
      dispatch({ 
        type: 'SET_PROJECT', 
        payload: { 
          svgContent: demoSVG,
          sequences: [
            {
              id: 'demo1',
              name: 'Warm Intro',
              duration: 4000,
              elements: [
                {
                  id: 'G1',
                  effects: [
                    { type: 'colorFade', from: '#8B4513', to: '#ff4400', duration: 3000 }
                  ]
                },
                {
                  id: 'B1',
                  effects: [
                    { type: 'fadeIn', duration: 2000 }
                  ]
                }
              ]
            },
            {
              id: 'demo2',
              name: 'Rainbow Party',
              duration: 6000,
              elements: [
                {
                  id: 'G1',
                  effects: [
                    { type: 'rainbow', duration: 5000, cycles: 1 }
                  ]
                },
                {
                  id: 'G2',
                  effects: [
                    { type: 'rainbow', duration: 4000, cycles: 1 }
                  ]
                },
                {
                  id: 'B2',
                  effects: [
                    { type: 'pulse', color: '#ff6b35', intensity: 0.8, duration: 2000 }
                  ]
                }
              ]
            },
            {
              id: 'demo3',
              name: 'Fenster Erwachen',
              duration: 8000,
              elements: [
                {
                  id: 'F1',
                  effects: [
                    { type: 'windowGlow', glowColor: '#FFE4B5', intensity: 0.8, duration: 2000 }
                  ]
                },
                {
                  id: 'F2',
                  effects: [
                    { type: 'windowGlow', glowColor: '#FFE4B5', intensity: 0.8, duration: 2000 }
                  ]
                },
                {
                  id: 'F3',
                  effects: [
                    { type: 'windowGlow', glowColor: '#FFE4B5', intensity: 0.8, duration: 2000 }
                  ]
                },
                {
                  id: 'F4',
                  effects: [
                    { type: 'windowGlow', glowColor: '#FFE4B5', intensity: 0.8, duration: 2000 }
                  ]
                }
              ]
            },
            {
              id: 'demo4',
              name: 'Dynamic Movement',
              duration: 5000,
              elements: [
                {
                  id: 'G3',
                  effects: [
                    { type: 'scale', scale: 1.3, duration: 2500 },
                    { type: 'colorFade', from: '#00ff00', to: '#ff00ff', duration: 3000 }
                  ]
                },
                {
                  id: 'B4',
                  effects: [
                    { type: 'rotate', degrees: 360, duration: 4000 },
                    { type: 'pulse', color: '#ffff00', intensity: 0.6, duration: 1500 }
                  ]
                }
              ]
            },
            {
              id: 'demo5',
              name: 'Ambient Glow',
              duration: 8000,
              elements: [
                {
                  id: 'G1',
                  effects: [
                    { type: 'breathe', breathRate: 0.3, depth: 0.4, duration: 8000 }
                  ]
                },
                {
                  id: 'G2',
                  effects: [
                    { type: 'warmCool', warmColor: '#ffaa44', coolColor: '#4488ff', duration: 8000 }
                  ]
                },
                {
                  id: 'F5',
                  effects: [
                    { type: 'warmWindowLight', warmColor: '#FFA500', intensity: 0.7, duration: 8000 }
                  ]
                },
                {
                  id: 'F6',
                  effects: [
                    { type: 'warmWindowLight', warmColor: '#FFA500', intensity: 0.7, duration: 8000 }
                  ]
                }
              ]
            }
          ]
        }
      });
    }
  }, []);

  // Initialize Group Engine when SVG content changes
  useEffect(() => {
    if (state.project.svgContent) {
      setGroupEngine(new GroupEffectsEngine(state.project.svgContent));
    }
  }, [state.project.svgContent]);

  // Function to update deck layer opacities
  const updateDeckOpacities = useCallback(() => {
    const deckALayer = document.getElementById('deck-a-layer');
    const deckBLayer = document.getElementById('deck-b-layer');

    if (deckALayer && deckBLayer) {
      const crossfadeMix = state.player.crossfadeValue / 100;
      const masterOpacity = state.player.masterVolume / 100;

      const deckAOpacity = (1 - crossfadeMix) * masterOpacity;
      const deckBOpacity = crossfadeMix * masterOpacity;

      deckALayer.style.opacity = deckAOpacity;
      deckBLayer.style.opacity = deckBOpacity;
    }
  }, [state.player.crossfadeValue, state.player.masterVolume]);

  // Function to update design layer visibility
  const updateDesignLayerVisibility = useCallback(() => {
    const designLayer = document.getElementById('design-layer');
    if (designLayer) {
      const isAnyDeckPlaying = state.player.deckA.isPlaying || state.player.deckB.isPlaying;
      if (isAnyDeckPlaying) {
        designLayer.style.opacity = '0.2';
        designLayer.style.pointerEvents = 'auto';
      } else {
        designLayer.style.opacity = '0.8';
        designLayer.style.pointerEvents = 'auto';
      }
    }
  }, [state.player.deckA.isPlaying, state.player.deckB.isPlaying]);

  // Update deck opacities when crossfade or master volume changes
  useEffect(() => {
    updateDeckOpacities();
  }, [updateDeckOpacities]);

  // Update design layer visibility when deck playing states change
  useEffect(() => {
    updateDesignLayerVisibility();
  }, [updateDesignLayerVisibility]);

  // Auto-save functionality
  useEffect(() => {
    clearTimeout(autoSaveTimer.current);
    autoSaveTimer.current = setTimeout(() => {
      saveToLocalStorage();
    }, 5000);
  }, [state.project]);

  const saveToLocalStorage = useCallback(() => {
    try {
      const saved = JSON.stringify(state.project);
      console.log('Auto-saved project');
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'success',
          message: 'Projekt automatisch gespeichert',
          duration: 2000
        }
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [state.project]);

  const loadFromLocalStorage = useCallback(() => {
    try {
      console.log('Load from localStorage would happen here');
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'success',
          message: 'Projekt wiederhergestellt',
          duration: 3000
        }
      });
    } catch (error) {
      console.error('Load failed:', error);
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyboard = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            saveToLocalStorage();
            break;
          case 'z':
            e.preventDefault();
            dispatch({ type: 'UNDO' });
            break;
          case 'y':
            e.preventDefault();
            dispatch({ type: 'REDO' });
            break;
          case ' ':
            e.preventDefault();
            togglePlayback();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyboard);
    return () => window.removeEventListener('keydown', handleKeyboard);
  }, [saveToLocalStorage]);

  // SVG interaction handlers
  const setupSvgInteractions = useCallback(() => {
    if (!svgRef.current) return;

    const elements = svgRef.current.querySelectorAll('.balken, .gefach, .fenster');
    elements.forEach(element => {
      element.style.cursor = 'pointer';
      element.style.transition = 'all 0.3s ease';
      
      element.addEventListener('click', (e) => {
        e.stopPropagation();
        selectElement(element);
      });

      element.addEventListener('mouseenter', () => {
        if (!state.player.isPlaying) {
          if (element.classList.contains('fenster')) {
            element.style.filter = 'drop-shadow(0 0 8px #87CEEB) brightness(1.3)';
          } else if (element.classList.contains('balken')) {
            element.style.filter = 'brightness(1.2)';
          } else if (element.classList.contains('gefach')) {
            element.style.filter = 'brightness(1.1)';
          }
        }
      });

      element.addEventListener('mouseleave', () => {
        if (!state.player.isPlaying) {
          element.style.filter = '';
        }
      });
    });
  }, [state.player.isPlaying]);

  const selectElement = (element) => {
    if (state.editor.selectedElement) {
      state.editor.selectedElement.style.outline = '';
    }

    element.style.outline = '3px solid #00ff00';
    element.style.outlineOffset = '2px';
    
    dispatch({ 
      type: 'UPDATE_EDITOR', 
      payload: { 
        selectedElement: element,
        selectedElementId: element.id 
      } 
    });
  };

  const togglePlayback = useCallback(() => {
    if (state.player.isPlaying) {
      effectEngine.stopAllAnimations();
      dispatch({ type: 'UPDATE_PLAYER', payload: { isPlaying: false } });
    } else {
      playCurrentSequence();
      dispatch({ type: 'UPDATE_PLAYER', payload: { isPlaying: true } });
    }
  }, [state.player.isPlaying, effectEngine]);

  const playCurrentSequence = useCallback(() => {
    if (!state.player.currentSequence) return;

    const sequence = state.player.currentSequence;
    const chaseGroups = new Map(); // Track active animations by chase group

    // Collect all effects with timing and sort by start time
    const allEffects = [];
    sequence.elements.forEach(element => {
      element.effects.forEach(effect => {
        allEffects.push({
          elementId: element.id,
          effect: effect,
          delay: effect.delay || effect.startTime || 0
        });
      });
    });

    // Sort by delay to ensure proper chase order
    allEffects.sort((a, b) => a.delay - b.delay);

    allEffects.forEach(({ elementId, effect, delay }) => {
      const startEffect = () => {
        // Handle chase behavior - for looping chase effects
        if (effect.stopPrevious && effect.chaseGroup) {
          // For chase loops, we need to stop ALL other animations in this chase group
          if (effect.isChaseLoop) {
            // Stop all animations for elements in this chase group
            const chaseKey = `${effect.chaseGroup}_current`;
            const currentAnimationId = chaseGroups.get(chaseKey);
            if (currentAnimationId) {
              console.log(`Stopping previous chase animation for element ${elementId}, cycle ${effect.cycle}, index ${effect.chaseIndex}`);
              effectEngine.stopAnimation(currentAnimationId);
            }
          } else {
            // Regular chase behavior
            const previousAnimationId = chaseGroups.get(effect.chaseGroup);
            if (previousAnimationId) {
              effectEngine.stopAnimation(previousAnimationId);
            }
          }
        }

        // Start the new effect
        let animationId;
        if (effect.beatSync && beatEngine) {
          animationId = effectEngine.startBeatSyncAnimation(elementId, effect, effect.quantization || 'beat');
        } else {
          animationId = effectEngine.startAnimation(elementId, effect, effect.duration);
        }

        if (effect.isChaseLoop) {
          console.log(`Starting chase animation for element ${elementId}, cycle ${effect.cycle}, index ${effect.chaseIndex}, animationId: ${animationId}`);
        }

        // Track this animation for chase behavior
        if (effect.stopPrevious && effect.chaseGroup && animationId) {
          if (effect.isChaseLoop) {
            chaseGroups.set(`${effect.chaseGroup}_current`, animationId);
          } else {
            chaseGroups.set(effect.chaseGroup, animationId);
          }
        }
      };

      if (delay === 0) {
        startEffect();
      } else {
        setTimeout(startEffect, delay);
      }
    });
  }, [state.player.currentSequence, effectEngine, beatEngine]);

  // Helper function to start deck animations
  const startDeckAnimations = useCallback((deck) => {
    const deckData = deck === 'A' ? state.player.deckA : state.player.deckB;
    if (!deckData.sequence) return;

    const chaseGroups = new Map();
    const allEffects = [];

    deckData.sequence.elements.forEach(element => {
      element.effects.forEach(effect => {
        allEffects.push({
          elementId: element.id,
          effect: effect,
          delay: effect.delay || effect.startTime || 0
        });
      });
    });

    allEffects.sort((a, b) => a.delay - b.delay);

    allEffects.forEach(({ elementId, effect, delay }) => {
      const startEffect = () => {
        if (effect.stopPrevious && effect.chaseGroup) {
          if (effect.isChaseLoop) {
            const chaseKey = `${effect.chaseGroup}_current`;
            const currentAnimationId = chaseGroups.get(chaseKey);
            if (currentAnimationId) {
              effectEngine.stopAnimation(currentAnimationId);
            }
          } else {
            const previousAnimationId = chaseGroups.get(effect.chaseGroup);
            if (previousAnimationId) {
              effectEngine.stopAnimation(previousAnimationId);
            }
          }
        }

        let animationId;
        if (effect.beatSync && beatEngine) {
          animationId = effectEngine.startBeatSyncAnimation(elementId, effect, effect.quantization || 'beat');
        } else {
          animationId = effectEngine.startAnimation(elementId, effect, effect.duration, deck);
        }

        if (effect.stopPrevious && effect.chaseGroup && animationId) {
          if (effect.isChaseLoop) {
            chaseGroups.set(`${effect.chaseGroup}_current`, animationId);
          } else {
            chaseGroups.set(effect.chaseGroup, animationId);
          }
        }
      };

      if (delay === 0) {
        startEffect();
      } else {
        setTimeout(startEffect, delay);
      }
    });
  }, [state.player.deckA, state.player.deckB, effectEngine, beatEngine]);

  const openProjectionWindow = useCallback(() => {
    const projWindow = window.open(
      '',
      'projection',
      'width=1920,height=1080,fullscreen=yes'
    );

    if (projWindow) {
      // Wait for the window to load before accessing its document
      const initializeProjectionWindow = () => {
        try {
          projWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Fachwerkwand Projektion</title>
                <style>
                  body {
                    margin: 0;
                    padding: 0;
                    background: black;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    overflow: hidden;
                    cursor: none;
                  }
                  #projection-svg {
                    max-width: 100vw;
                    max-height: 100vh;
                    width: auto;
                    height: auto;
                  }
                  .calibration-grid {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image:
                      linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
                    background-size: 50px 50px;
                    pointer-events: none;
                    display: none;
                  }
                  .calibration-grid.active {
                    display: block;
                  }
                </style>
              </head>
              <body>
                <div class="calibration-grid" id="calibration"></div>
                <div id="projection-container">
                  <div id="deck-a-layer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;"></div>
                  <div id="deck-b-layer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 2;"></div>
                </div>
                <script>
                  const activeAnimations = new Map();

                  function initializeDeckLayers(svgContent) {
                    const parser = new DOMParser();
                    const svgDoc = parser.parseFromString(svgContent, 'image/svg+xml');
                    const originalSvg = svgDoc.documentElement;

                    const deckALayer = document.getElementById('deck-a-layer');
                    const deckBLayer = document.getElementById('deck-b-layer');

                    if (deckALayer && deckBLayer && originalSvg) {
                      const svgA = originalSvg.cloneNode(true);
                      const svgB = originalSvg.cloneNode(true);

                      svgA.querySelectorAll('[id]').forEach(element => {
                        const originalId = element.id;
                        element.id = \`\${originalId}_deckA\`;
                      });

                      svgB.querySelectorAll('[id]').forEach(element => {
                        const originalId = element.id;
                        element.id = \`\${originalId}_deckB\`;
                      });

                      deckALayer.appendChild(svgA);
                      deckBLayer.appendChild(svgB);
                    }
                  }

                  initializeDeckLayers(\`${state.project.svgContent}\`);

                  function updateDeckOpacities(crossfadeValue, masterVolume) {
                    const deckALayer = document.getElementById('deck-a-layer');
                    const deckBLayer = document.getElementById('deck-b-layer');

                    if (deckALayer && deckBLayer) {
                      const crossfadeMix = crossfadeValue / 100;
                      const masterOpacity = masterVolume / 100;

                      const deckAOpacity = (1 - crossfadeMix) * masterOpacity;
                      const deckBOpacity = crossfadeMix * masterOpacity;

                      deckALayer.style.opacity = deckAOpacity;
                      deckBLayer.style.opacity = deckBOpacity;
                    }
                  }

                  updateDeckOpacities(${state.player.crossfadeValue}, ${state.player.masterVolume});

              function interpolateColor(color1, color2, factor) {
                const hex1 = color1.replace('#', '');
                const hex2 = color2.replace('#', '');
                const r1 = parseInt(hex1.substr(0, 2), 16);
                const g1 = parseInt(hex1.substr(2, 2), 16);
                const b1 = parseInt(hex1.substr(4, 2), 16);
                const r2 = parseInt(hex2.substr(0, 2), 16);
                const g2 = parseInt(hex2.substr(2, 2), 16);
                const b2 = parseInt(hex2.substr(4, 2), 16);
                const r = Math.round(r1 + (r2 - r1) * factor);
                const g = Math.round(g1 + (g2 - g1) * factor);
                const b = Math.round(b1 + (b2 - b1) * factor);
                return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
              }

              function applyEasing(t, type) {
                switch (type) {
                  case 'easeIn': return t * t;
                  case 'easeOut': return 1 - (1 - t) * (1 - t);
                  case 'easeInOut': return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
                  case 'bounce': return 1 - Math.abs(Math.sin(t * Math.PI * 2) * (1 - t));
                  case 'elastic': return Math.sin(t * Math.PI * 6) * Math.pow(2, -10 * t) + 1;
                  default: return t;
                }
              }

              function applyColorToElement(element, color) {
                if (element.tagName === 'g') {
                  const children = element.querySelectorAll('rect, circle, path, polygon, ellipse');
                  children.forEach(child => {
                    child.setAttribute('fill', color);
                  });
                } else if (element.setAttribute) {
                  element.setAttribute('fill', color);
                } else {
                  element.style.backgroundColor = color;
                }
              }

              function applyEffectFrame(element, effect, progress, deck = 'A') {
                if (!element) return;

                const eased = applyEasing(progress, effect.easing || 'linear');
                let transforms = [];

                switch (effect.type) {
                  case 'colorFade':
                    const colorProgress = (Math.sin(progress * Math.PI * 2) + 1) / 2;
                    applyColorToElement(element, interpolateColor(effect.from, effect.to, colorProgress));
                    break;

                  case 'rainbow':
                    const hue = (progress * 360 * (effect.cycles || 1)) % 360;
                    applyColorToElement(element, \`hsl(\${hue}, 100%, 50%)\`);
                    break;

                  case 'strobe':
                    const strobeFreq = effect.frequency || 10;
                    const strobePhase = Math.sin(progress * Math.PI * 2 * strobeFreq);
                    applyColorToElement(element, strobePhase > 0 ? (effect.color || '#ffffff') : '#000000');
                    break;

                  case 'pulse':
                    const pulseIntensity = effect.intensity || 0.8;
                    const pulseValue = 0.5 + (Math.sin(progress * Math.PI * 4) * pulseIntensity * 0.5);
                    applyColorToElement(element, interpolateColor('#000000', effect.color || '#00ff00', pulseValue));
                    break;

                  case 'windowGlow':
                    const glowColor = effect.glowColor || '#FFE4B5';
                    const glowIntensity = effect.intensity || 0.9;
                    const currentGlowIntensity = glowIntensity * (0.5 + 0.5 * Math.sin(progress * Math.PI * 2));
                    element.style.filter = \`drop-shadow(0 0 \${effect.spreadRadius || 8}px \${glowColor}) brightness(\${100 + currentGlowIntensity * 50}%)\`;
                    break;

                  case 'warmWindowLight':
                    const warmLightColor = effect.warmColor || '#FFA500';
                    const lightIntensity = effect.intensity || 0.7;
                    const flicker = effect.flicker || 0.1;
                    const flickerValue = 1 + (Math.random() - 0.5) * flicker;
                    const currentLightIntensity = lightIntensity * flickerValue;
                    
                    element.style.filter = \`drop-shadow(0 0 12px \${warmLightColor})\`;
                    element.style.opacity = (effect.opacity || 0.8) * currentLightIntensity;
                    break;

                  case 'breathe':
                    const breathRate = effect.breathRate || 0.2;
                    const depth = effect.depth || 0.3;
                    const asymmetry = effect.asymmetry || 0.5;
                    
                    const cyclePos = (progress * breathRate) % 1;
                    let breathValue;
                    
                    if (cyclePos < asymmetry) {
                      breathValue = Math.sin((cyclePos / asymmetry) * Math.PI * 0.5);
                    } else {
                      const exhalePos = (cyclePos - asymmetry) / (1 - asymmetry);
                      breathValue = Math.cos(exhalePos * Math.PI * 0.5);
                    }
                    
                    const opacity = 1 - (depth * 0.5) + (depth * 0.5 * breathValue);
                    element.style.opacity = opacity;
                    
                    const brightness = 100 - (depth * 20) + (depth * 20 * breathValue);
                    element.style.filter = \`brightness(\${brightness}%)\`;
                    break;

                  default:
                    break;
                }

                if (transforms.length > 0) {
                  element.style.transform = transforms.join(' ');
                }
              }

              const deckAnimations = { A: new Set(), B: new Set() };
              let currentCrossfadeValue = 50;
              let currentMasterVolume = 100;

              window.addEventListener('message', function(event) {
                const { type, elementId, effect, duration, animationId, deck, crossfadeValue, masterVolume } = event.data;

                switch (type) {
                  case 'startAnimation':
                    const startTime = performance.now();
                    if (crossfadeValue !== undefined) {
                      currentCrossfadeValue = crossfadeValue;
                    }
                    if (masterVolume !== undefined) {
                      currentMasterVolume = masterVolume;
                    }

                    updateDeckOpacities(currentCrossfadeValue, currentMasterVolume);

                    const animate = (currentTime) => {
                      const elapsed = currentTime - startTime;
                      const cycleProgress = (elapsed % duration) / duration;

                      const element = document.getElementById(elementId);
                      if (element) {
                        applyEffectFrame(element, effect, cycleProgress, deck);
                      }

                      if (activeAnimations.has(animationId)) {
                        activeAnimations.set(animationId, requestAnimationFrame(animate));
                      }
                    };

                    activeAnimations.set(animationId, requestAnimationFrame(animate));
                    if (deck) {
                      deckAnimations[deck].add(animationId);
                    }
                    break;

                  case 'stopAnimation':
                    const frameId = activeAnimations.get(animationId);
                    if (frameId) {
                      cancelAnimationFrame(frameId);
                      activeAnimations.delete(animationId);
                      deckAnimations.A.delete(animationId);
                      deckAnimations.B.delete(animationId);
                    }
                    break;

                  case 'stopDeckAnimations':
                    const deckAnimationIds = Array.from(deckAnimations[deck]);
                    deckAnimationIds.forEach(id => {
                      const frameId = activeAnimations.get(id);
                      if (frameId) {
                        cancelAnimationFrame(frameId);
                        activeAnimations.delete(id);
                      }
                    });
                    deckAnimations[deck].clear();
                    break;

                  case 'stopAllAnimations':
                    activeAnimations.forEach(frameId => cancelAnimationFrame(frameId));
                    activeAnimations.clear();
                    deckAnimations.A.clear();
                    deckAnimations.B.clear();
                    break;

                  case 'updateCrossfade':
                    if (crossfadeValue !== undefined) {
                      currentCrossfadeValue = crossfadeValue;
                    }
                    if (masterVolume !== undefined) {
                      currentMasterVolume = masterVolume;
                    }
                    updateDeckOpacities(currentCrossfadeValue, currentMasterVolume);
                    break;
                  }
                });
              </script>
            </body>
          </html>
        `);

          projWindow.document.close();

          dispatch({ type: 'UPDATE_UI', payload: { projectionWindow: projWindow } });
          window.projectionWindow = projWindow;
        } catch (error) {
          console.error('Error initializing projection window:', error);
          // Retry after a short delay
          setTimeout(initializeProjectionWindow, 100);
        }
      };

      // Try to initialize immediately, but if it fails, the catch block will retry
      initializeProjectionWindow();
    }
  }, [state.project.svgContent]);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    
    if (file.type === 'image/svg+xml') {
      reader.onload = (e) => {
        dispatch({ 
          type: 'SET_PROJECT', 
          payload: { svgContent: e.target.result } 
        });
        dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });
      };
      reader.readAsText(file);
    } else if (file.type === 'application/json') {
      reader.onload = (e) => {
        try {
          const project = JSON.parse(e.target.result);
          dispatch({ type: 'SET_PROJECT', payload: project });
          dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });
        } catch (error) {
          dispatch({
            type: 'ADD_NOTIFICATION',
            payload: {
              id: Date.now(),
              type: 'error',
              message: 'Fehler beim Laden der Datei',
              duration: 5000
            }
          });
        }
      };
      reader.readAsText(file);
    }
  };

  const exportProject = () => {
    const projectData = {
      ...state.project,
      exportDate: new Date().toISOString(),
      version: '2.0'
    };

    const blob = new Blob([JSON.stringify(projectData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${state.project.name.replace(/\s+/g, '_')}_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        message: 'Projekt exportiert',
        duration: 3000
      }
    });
  };

  const createSequence = () => {
    const newSequence = {
      id: `seq_${Date.now()}`,
      name: `Sequenz ${state.project.sequences.length + 1}`,
      duration: 4000,
      elements: [],
      bpmSync: false,
      loop: false
    };

    const updatedSequences = [...state.project.sequences, newSequence];
    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });
    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });

    return newSequence;
  };

  const editSequence = (sequence) => {
    const newName = prompt('Sequenz-Name bearbeiten:', sequence.name);
    if (newName && newName.trim() !== '') {
      const updatedSequences = state.project.sequences.map(seq =>
        seq.id === sequence.id
          ? { ...seq, name: newName.trim() }
          : seq
      );

      dispatch({
        type: 'SET_PROJECT',
        payload: { sequences: updatedSequences }
      });
      dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });
    }
  };

  const copySequence = (sequence) => {
    const copiedSequence = {
      ...sequence,
      id: `seq_${Date.now()}`,
      name: `${sequence.name} (Kopie)`
    };

    const updatedSequences = [...state.project.sequences, copiedSequence];
    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });
    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });
  };

  const addEffectToSequence = (effectType, params) => {
    if (!state.editor.selectedElement || !state.player.currentSequence) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'warning',
          message: 'Bitte wähle ein Element und eine Sequenz',
          duration: 3000
        }
      });
      return;
    }

    const elementId = state.editor.selectedElement.id;
    const sequenceId = state.player.currentSequence.id;

    const updatedSequences = state.project.sequences.map(seq => {
      if (seq.id === sequenceId) {
        const elementIndex = seq.elements.findIndex(el => el.id === elementId);

        if (elementIndex >= 0) {
          return {
            ...seq,
            elements: seq.elements.map((el, index) =>
              index === elementIndex
                ? { ...el, effects: [...el.effects, { type: effectType, ...params }] }
                : el
            )
          };
        } else {
          return {
            ...seq,
            elements: [...seq.elements, {
              id: elementId,
              effects: [{ type: effectType, ...params }]
            }]
          };
        }
      }
      return seq;
    });

    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });
    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        message: `Effekt "${effectType}" zu Sequenz "${state.player.currentSequence.name}" hinzugefügt`,
        duration: 2000
      }
    });

    effectEngine.startAnimation(elementId, { type: effectType, ...params }, params.duration || 2000);
  };

  // New function to add group effects to sequence
  const addGroupEffectToSequence = (groupEffect) => {
    if (!state.player.currentSequence || !groupEngine) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'warning',
          message: 'Bitte wähle eine Sequenz und lade ein SVG',
          duration: 3000
        }
      });
      return;
    }

    const { targetGroup, pattern, effect, timing } = groupEffect;
    const elements = groupEngine.groups[targetGroup];

    if (!elements || elements.length === 0) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'warning',
          message: `Keine Elemente in Gruppe "${targetGroup}" gefunden`,
          duration: 3000
        }
      });
      return;
    }

    // Generate the pattern intervals with BPM consideration
    const patternInstance = groupEngine.getGroupPatterns()[pattern];

    // Adjust timing for BPM if beatSync is enabled
    let adjustedTiming = { ...timing };
    if (timing.beatSync && beatEngine) {
      const beatDuration = 60000 / beatEngine.bpm; // Duration of one beat in ms
      const beatMultiplier = {
        'beat': 1,
        'bar': 4,
        '2-bar': 8,
        '4-bar': 16
      }[timing.quantization] || 1;

      // Convert timing to BPM-based values
      adjustedTiming = {
        ...timing,
        duration: beatDuration * beatMultiplier * (timing.duration / 1000), // Convert to beat-based duration
        elementDuration: beatDuration * (timing.elementDuration / 1000) // Convert to beat-based element duration
      };
    }

    const intervals = patternInstance.apply(elements, adjustedTiming);

    const sequenceId = state.player.currentSequence.id;

    // Convert intervals to sequence elements with proper timing
    const newSequenceElements = intervals.map(interval => ({
      id: interval.elementId,
      effects: [{
        type: effect.type,
        ...effect.parameters,
        duration: interval.duration || timing.elementDuration,
        startTime: interval.startTime || 0,
        // Add timing metadata for sequence player
        delay: interval.startTime || 0,
        beatOffset: interval.beatOffset || 0,
        // Add chase behavior metadata
        stopPrevious: interval.stopPrevious || false,
        chaseIndex: interval.chaseIndex,
        chaseGroup: groupEffect.name // Group identifier for chase effects
      }]
    }));

    // Update the sequence
    const updatedSequences = state.project.sequences.map(seq => {
      if (seq.id === sequenceId) {
        // Merge with existing elements
        const existingElements = seq.elements || [];
        const mergedElements = [...existingElements];

        newSequenceElements.forEach(newElement => {
          const existingIndex = mergedElements.findIndex(el => el.id === newElement.id);
          if (existingIndex >= 0) {
            // Add effects to existing element
            mergedElements[existingIndex] = {
              ...mergedElements[existingIndex],
              effects: [...mergedElements[existingIndex].effects, ...newElement.effects]
            };
          } else {
            // Add new element
            mergedElements.push(newElement);
          }
        });

        return {
          ...seq,
          elements: mergedElements,
          duration: Math.max(seq.duration, timing.duration + Math.max(...intervals.map(i => i.startTime || 0)))
        };
      }
      return seq;
    });

    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });
    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        message: `Group Effect "${groupEffect.name}" zu Sequenz "${state.player.currentSequence.name}" hinzugefügt (${elements.length} Elemente)`,
        duration: 3000
      }
    });
  };

  const deleteSequence = (sequenceId) => {
    if (state.project.sequences.length <= 1) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'warning',
          message: 'Mindestens eine Sequenz muss vorhanden sein',
          duration: 3000
        }
      });
      return;
    }

    const updatedSequences = state.project.sequences.filter(seq => seq.id !== sequenceId);

    let newCurrentSequence = state.player.currentSequence;
    if (state.player.currentSequence?.id === sequenceId) {
      newCurrentSequence = updatedSequences[0] || null;
    }

    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });

    if (newCurrentSequence !== state.player.currentSequence) {
      dispatch({
        type: 'UPDATE_PLAYER',
        payload: { currentSequence: newCurrentSequence }
      });
    }

    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        message: 'Sequenz gelöscht',
        duration: 2000
      }
    });
  };

  const removeEffectFromSequence = (sequenceId, elementId, effectIndex) => {
    const updatedSequences = state.project.sequences.map(seq => {
      if (seq.id === sequenceId) {
        return {
          ...seq,
          elements: seq.elements.map(el => {
            if (el.id === elementId) {
              return {
                ...el,
                effects: el.effects.filter((_, index) => index !== effectIndex)
              };
            }
            return el;
          }).filter(el => el.effects.length > 0)
        };
      }
      return seq;
    });

    dispatch({
      type: 'SET_PROJECT',
      payload: { sequences: updatedSequences }
    });
    dispatch({ type: 'ADD_TO_HISTORY', payload: state.project });

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        message: 'Effekt entfernt',
        duration: 2000
      }
    });
  };

  const toggleSequenceExpansion = (sequenceId) => {
    setExpandedSequences(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sequenceId)) {
        newSet.delete(sequenceId);
      } else {
        newSet.add(sequenceId);
      }
      return newSet;
    });
  };

  const applyGroupEffect = (groupEffect) => {
    if (!groupEngine) {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'error',
          message: 'Group Engine nicht initialisiert',
          duration: 3000
        }
      });
      return;
    }

    const result = effectEngine.startGroupEffect(groupEffect, groupEngine);

    if (result) {
      // Track the active group effect
      setActiveGroupEffects(prev => [...prev, {
        ...result,
        startTime: Date.now(),
        name: groupEffect.name
      }]);

      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'success',
          message: `Group Effect "${groupEffect.name}" applied to ${result.elementCount} elements`,
          duration: 2000
        }
      });
    } else {
      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'error',
          message: `Failed to apply group effect "${groupEffect.name}"`,
          duration: 3000
        }
      });
    }
  };

  const stopGroupEffect = (effectId) => {
    const effect = activeGroupEffects.find(e => e.id === effectId);
    if (effect) {
      // Stop all animations for this group effect
      effect.animationIds.forEach(animationId => {
        effectEngine.stopAnimation(animationId);
      });

      // Remove from active effects
      setActiveGroupEffects(prev => prev.filter(e => e.id !== effectId));

      dispatch({
        type: 'ADD_NOTIFICATION',
        payload: {
          id: Date.now(),
          type: 'info',
          message: `Stopped group effect "${effect.name}"`,
          duration: 2000
        }
      });
    }
  };

  const stopAllGroupEffects = () => {
    activeGroupEffects.forEach(effect => {
      effect.animationIds.forEach(animationId => {
        effectEngine.stopAnimation(animationId);
      });
    });

    setActiveGroupEffects([]);

    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'info',
        message: 'Stopped all group effects',
        duration: 2000
      }
    });
  };

  // Setup SVG with design layer and deck layers when content changes
  useEffect(() => {
    if (state.project.svgContent && svgRef.current) {
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(state.project.svgContent, 'image/svg+xml');
      const originalSvg = svgDoc.documentElement;

      const container = document.createElement('div');
      container.style.position = 'relative';
      container.style.width = '100%';
      container.style.height = '100%';

      const deckALayer = document.createElement('div');
      deckALayer.id = 'deck-a-layer';
      deckALayer.style.position = 'absolute';
      deckALayer.style.top = '0';
      deckALayer.style.left = '0';
      deckALayer.style.width = '100%';
      deckALayer.style.height = '100%';
      deckALayer.style.zIndex = '1';
      deckALayer.style.pointerEvents = 'none';

      const deckBLayer = document.createElement('div');
      deckBLayer.id = 'deck-b-layer';
      deckBLayer.style.position = 'absolute';
      deckBLayer.style.top = '0';
      deckBLayer.style.left = '0';
      deckBLayer.style.width = '100%';
      deckBLayer.style.height = '100%';
      deckBLayer.style.zIndex = '2';
      deckBLayer.style.pointerEvents = 'none';

      const designLayer = document.createElement('div');
      designLayer.id = 'design-layer';
      designLayer.style.position = 'absolute';
      designLayer.style.top = '0';
      designLayer.style.left = '0';
      designLayer.style.width = '100%';
      designLayer.style.height = '100%';
      designLayer.style.zIndex = '10';
      designLayer.style.pointerEvents = 'auto';

      const svgA = originalSvg.cloneNode(true);
      const svgB = originalSvg.cloneNode(true);
      const svgDesign = originalSvg.cloneNode(true);

      svgA.querySelectorAll('[id]').forEach(element => {
        const originalId = element.id;
        element.id = `${originalId}_deckA`;
      });

      svgB.querySelectorAll('[id]').forEach(element => {
        const originalId = element.id;
        element.id = `${originalId}_deckB`;
      });

      svgDesign.style.opacity = '0.3';
      svgDesign.style.fill = 'none';
      svgDesign.style.stroke = '#666';
      svgDesign.style.strokeWidth = '1';

      deckALayer.appendChild(svgA);
      deckBLayer.appendChild(svgB);
      designLayer.appendChild(svgDesign);

      container.appendChild(deckALayer);
      container.appendChild(deckBLayer);
      container.appendChild(designLayer);

      svgRef.current.innerHTML = '';
      svgRef.current.appendChild(container);

      updateDeckOpacities();
      updateDesignLayerVisibility();
      setupSvgInteractions();
    }
  }, [state.project.svgContent, setupSvgInteractions]);

  // Performance monitoring
  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      const currentTime = performance.now();
      frameCount++;

      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        const frameTime = (currentTime - lastTime) / frameCount;

        dispatch({
          type: 'UPDATE_PERFORMANCE',
          payload: {
            fps,
            frameTime,
            activeAnimations: effectEngine.engineStats.activeCount,
            memoryUsage: performance.memory?.usedJSHeapSize || 0
          }
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(measureFPS);
    };

    measureFPS();
  }, [effectEngine.engineStats.activeCount]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      <div className="w-full h-screen bg-gray-900 text-white flex flex-col">
        {/* Header Bar */}
        <header className="bg-gray-800 border-b border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Fachwerkwand Illumination System
              </h1>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${state.player.isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`} />
                <span className="text-sm text-gray-400">
                  {state.player.isPlaying ? 'Live' : 'Bereit'}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".svg,.json"
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current.click()}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Upload size={16} />
                <span className="text-sm">Import</span>
              </button>
              
              <button
                onClick={exportProject}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Download size={16} />
                <span className="text-sm">Export</span>
              </button>

              <div className="w-px h-6 bg-gray-700 mx-2" />

              <button
                onClick={togglePlayback}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-all ${
                  state.player.isPlaying 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-green-600 hover:bg-green-700'
                }`}
              >
                {state.player.isPlaying ? <Pause size={16} /> : <Play size={16} />}
                <span className="text-sm font-medium">
                  {state.player.isPlaying ? 'Stop' : 'Start'}
                </span>
              </button>

              <button
                onClick={() => effectEngine.stopAllAnimations()}
                className="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
              >
                <Square size={16} />
              </button>

              <div className="w-px h-6 bg-gray-700 mx-2" />

              <button
                onClick={openProjectionWindow}
                className="px-3 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Monitor size={16} />
                <span className="text-sm">Projektion</span>
              </button>

              <div className="w-px h-6 bg-gray-700 mx-2" />

              <div className="flex items-center gap-2 px-3 py-2 bg-gray-700 rounded-lg">
                <Music size={16} />
                <span className="text-sm font-mono">{state.beatEngine.bpm} BPM</span>
                <button
                  onClick={() => beatEngine.tapBeat()}
                  className="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded text-xs"
                >
                  TAP
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Sidebar - Live Performance Controls */}
          <aside className={`bg-gray-800 border-r border-gray-700 transition-all flex flex-col ${
            state.ui.sidebarCollapsed ? 'w-12' : 'w-80'
          }`}>
            <div className="p-4 border-b border-gray-700 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h2 className={`font-semibold ${state.ui.sidebarCollapsed ? 'hidden' : ''}`}>
                  Live Performance
                </h2>
                <button
                  onClick={() => dispatch({
                    type: 'UPDATE_UI',
                    payload: { sidebarCollapsed: !state.ui.sidebarCollapsed }
                  })}
                  className="p-1 hover:bg-gray-700 rounded"
                >
                  {state.ui.sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
                </button>
              </div>
            </div>

            {!state.ui.sidebarCollapsed && (
              <div className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-4">

                  {/* DJ Mixer Interface */}
                  <div>
                    <h3 className="text-lg font-bold mb-6 text-center bg-gradient-to-r from-blue-400 to-red-400 bg-clip-text text-transparent">
                      DJ MIXER
                    </h3>

                    <div className="space-y-6">
                      {/* Deck A */}
                      <div className="bg-gradient-to-br from-blue-900 to-blue-800 p-6 rounded-xl border-2 border-blue-500 shadow-lg">
                        <div className="text-center mb-4">
                          <span className="text-lg font-bold text-blue-300 tracking-wider">DECK A</span>
                          <div className="w-full h-1 bg-blue-500 rounded mt-2"></div>
                        </div>

                        <div className="mb-4">
                          <label className="block text-sm font-medium text-blue-200 mb-2">Sequence</label>
                          <select
                            value={state.player.deckA.sequence?.id || ''}
                            onChange={(e) => {
                              const sequence = state.project.sequences.find(s => s.id === e.target.value);
                              dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: { deckA: { ...state.player.deckA, sequence } }
                              });
                            }}
                            className="w-full px-3 py-2 text-sm bg-gray-700 border border-blue-400 rounded-lg focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Select Sequence</option>
                            {state.project.sequences.map(seq => (
                              <option key={seq.id} value={seq.id}>{seq.name}</option>
                            ))}
                          </select>
                        </div>

                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm font-medium text-blue-200">BPM</label>
                            <button
                              onClick={() => dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: {
                                  deckA: {
                                    ...state.player.deckA,
                                    isBpmSynced: !state.player.deckA.isBpmSynced,
                                    bpm: state.player.deckA.isBpmSynced ? state.player.deckA.bpm : state.beatEngine.bpm
                                  }
                                }
                              })}
                              className={`px-3 py-1 text-xs font-bold rounded-lg transition-colors ${
                                state.player.deckA.isBpmSynced
                                  ? 'bg-green-600 hover:bg-green-700 text-white'
                                  : 'bg-gray-600 hover:bg-gray-500 text-gray-200'
                              }`}
                            >
                              {state.player.deckA.isBpmSynced ? 'SYNC' : 'MANUAL'}
                            </button>
                          </div>
                          <input
                            type="number"
                            min="60"
                            max="200"
                            value={state.player.deckA.bpm}
                            onChange={(e) => dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: {
                                deckA: {
                                  ...state.player.deckA,
                                  bpm: parseInt(e.target.value),
                                  isBpmSynced: false
                                }
                              }
                            })}
                            className="w-full px-3 py-2 text-lg font-mono bg-gray-700 border border-blue-400 rounded-lg focus:ring-2 focus:ring-blue-500 text-center"
                            disabled={state.player.deckA.isBpmSynced}
                          />
                        </div>

                        <div className="mb-4">
                          <label className="block text-sm font-medium text-blue-200 mb-2">Speed</label>
                          <input
                            type="range"
                            min="0.05"
                            max="3"
                            step="0.05"
                            value={state.player.deckA.playbackSpeed}
                            onChange={(e) => dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: {
                                deckA: {
                                  ...state.player.deckA,
                                  playbackSpeed: parseFloat(e.target.value)
                                }
                              }
                            })}
                            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-blue"
                          />
                          <div className="text-center mt-2">
                            <span className="text-lg font-mono font-bold text-blue-300">{state.player.deckA.playbackSpeed.toFixed(2)}x</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <button
                            onClick={() => {
                              if (state.player.deckA.sequence) {
                                if (state.player.deckA.isPlaying) {
                                  effectEngine.stopDeckAnimations('A');
                                } else {
                                  startDeckAnimations('A');
                                }
                                dispatch({
                                  type: 'UPDATE_PLAYER',
                                  payload: {
                                    deckA: {
                                      ...state.player.deckA,
                                      isPlaying: !state.player.deckA.isPlaying
                                    }
                                  }
                                });
                              }
                            }}
                            className={`py-3 text-lg font-bold rounded-lg transition-all transform hover:scale-105 ${
                              state.player.deckA.isPlaying
                                ? 'bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-500/50'
                                : 'bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-500/50'
                            }`}
                            disabled={!state.player.deckA.sequence}
                          >
                            {state.player.deckA.isPlaying ? '⏹ STOP' : '▶ PLAY'}
                          </button>

                          <button
                            onClick={() => {
                              if (state.player.deckA.sequence && state.player.deckA.isPlaying) {
                                // Stop current animations
                                effectEngine.stopDeckAnimations('A');

                                // Restart with current settings after a small delay
                                setTimeout(() => {
                                  startDeckAnimations('A');
                                }, 50);
                              }
                            }}
                            className="py-3 text-sm font-bold bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all transform hover:scale-105 shadow-lg shadow-blue-500/50"
                            disabled={!state.player.deckA.sequence || !state.player.deckA.isPlaying}
                          >
                            🔄 APPLY
                          </button>
                        </div>
                      </div>

                      {/* Deck B */}
                      <div className="bg-gradient-to-br from-red-900 to-red-800 p-6 rounded-xl border-2 border-red-500 shadow-lg">
                        <div className="text-center mb-4">
                          <span className="text-lg font-bold text-red-300 tracking-wider">DECK B</span>
                          <div className="w-full h-1 bg-red-500 rounded mt-2"></div>
                        </div>

                        <div className="mb-4">
                          <label className="block text-sm font-medium text-red-200 mb-2">Sequence</label>
                          <select
                            value={state.player.deckB.sequence?.id || ''}
                            onChange={(e) => {
                              const sequence = state.project.sequences.find(s => s.id === e.target.value);
                              dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: { deckB: { ...state.player.deckB, sequence } }
                              });
                            }}
                            className="w-full px-3 py-2 text-sm bg-gray-700 border border-red-400 rounded-lg focus:ring-2 focus:ring-red-500"
                          >
                            <option value="">Select Sequence</option>
                            {state.project.sequences.map(seq => (
                              <option key={seq.id} value={seq.id}>{seq.name}</option>
                            ))}
                          </select>
                        </div>

                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <label className="text-sm font-medium text-red-200">BPM</label>
                            <button
                              onClick={() => dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: {
                                  deckB: {
                                    ...state.player.deckB,
                                    isBpmSynced: !state.player.deckB.isBpmSynced,
                                    bpm: state.player.deckB.isBpmSynced ? state.player.deckB.bpm : state.beatEngine.bpm
                                  }
                                }
                              })}
                              className={`px-3 py-1 text-xs font-bold rounded-lg transition-colors ${
                                state.player.deckB.isBpmSynced
                                  ? 'bg-green-600 hover:bg-green-700 text-white'
                                  : 'bg-gray-600 hover:bg-gray-500 text-gray-200'
                              }`}
                            >
                              {state.player.deckB.isBpmSynced ? 'SYNC' : 'MANUAL'}
                            </button>
                          </div>
                          <input
                            type="number"
                            min="60"
                            max="200"
                            value={state.player.deckB.bpm}
                            onChange={(e) => dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: {
                                deckB: {
                                  ...state.player.deckB,
                                  bpm: parseInt(e.target.value),
                                  isBpmSynced: false
                                }
                              }
                            })}
                            className="w-full px-3 py-2 text-lg font-mono bg-gray-700 border border-red-400 rounded-lg focus:ring-2 focus:ring-red-500 text-center"
                            disabled={state.player.deckB.isBpmSynced}
                          />
                        </div>

                        <div className="mb-4">
                          <label className="block text-sm font-medium text-red-200 mb-2">Speed</label>
                          <input
                            type="range"
                            min="0.05"
                            max="3"
                            step="0.05"
                            value={state.player.deckB.playbackSpeed}
                            onChange={(e) => dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: {
                                deckB: {
                                  ...state.player.deckB,
                                  playbackSpeed: parseFloat(e.target.value)
                                }
                              }
                            })}
                            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-red"
                          />
                          <div className="text-center mt-2">
                            <span className="text-lg font-mono font-bold text-red-300">{state.player.deckB.playbackSpeed.toFixed(2)}x</span>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <button
                            onClick={() => {
                              if (state.player.deckB.sequence) {
                                if (state.player.deckB.isPlaying) {
                                  effectEngine.stopDeckAnimations('B');
                                } else {
                                  startDeckAnimations('B');
                                }
                                dispatch({
                                  type: 'UPDATE_PLAYER',
                                  payload: {
                                    deckB: {
                                      ...state.player.deckB,
                                      isPlaying: !state.player.deckB.isPlaying
                                    }
                                  }
                                });
                              }
                            }}
                            className={`py-3 text-lg font-bold rounded-lg transition-all transform hover:scale-105 ${
                              state.player.deckB.isPlaying
                                ? 'bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-500/50'
                                : 'bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-500/50'
                            }`}
                            disabled={!state.player.deckB.sequence}
                          >
                            {state.player.deckB.isPlaying ? '⏹ STOP' : '▶ PLAY'}
                          </button>

                          <button
                            onClick={() => {
                              if (state.player.deckB.sequence && state.player.deckB.isPlaying) {
                                // Stop current animations
                                effectEngine.stopDeckAnimations('B');

                                // Restart with current settings after a small delay
                                setTimeout(() => {
                                  startDeckAnimations('B');
                                }, 50);
                              }
                            }}
                            className="py-3 text-sm font-bold bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all transform hover:scale-105 shadow-lg shadow-blue-500/50"
                            disabled={!state.player.deckB.sequence || !state.player.deckB.isPlaying}
                          >
                            🔄 APPLY
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Beat Sync Accordion */}
                  <div className="bg-gray-700 rounded-lg overflow-hidden">
                    <button
                      onClick={() => dispatch({
                        type: 'UPDATE_UI',
                        payload: { beatSyncExpanded: !state.ui.beatSyncExpanded }
                      })}
                      className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-600 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Beat Sync</span>
                        <BeatIndicator beatEngine={beatEngine} />
                      </div>
                      <ChevronDown
                        size={16}
                        className={`transform transition-transform ${
                          state.ui.beatSyncExpanded ? 'rotate-180' : ''
                        }`}
                      />
                    </button>

                    {state.ui.beatSyncExpanded && (
                      <div className="p-4 bg-gray-750 border-t border-gray-600">
                        <BeatSyncPanel beatEngine={beatEngine} />
                      </div>
                    )}
                  </div>

                  {/* Crossfade Control */}
                  <div className="pt-6 border-t border-gray-700">
                    <div className="text-center mb-6">
                      <span className="text-lg font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-red-400 bg-clip-text text-transparent">
                        CROSSFADER
                      </span>
                    </div>

                    <div className="bg-gradient-to-r from-gray-800 to-gray-700 p-6 rounded-xl border border-gray-600 shadow-lg">
                      <div className="flex items-center gap-4 mb-4">
                        <span className="text-lg font-bold text-blue-400 w-8 text-center">A</span>
                        <div className="flex-1 relative">
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={state.player.crossfadeValue}
                            onChange={(e) => {
                              const newValue = parseInt(e.target.value);
                              dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: { crossfadeValue: newValue }
                              });
                              effectEngine.updateCrossfade(newValue, state.player.masterVolume);
                            }}
                            className="w-full h-3 bg-gray-600 rounded-lg appearance-none cursor-pointer crossfader"
                            style={{
                              background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${100-state.player.crossfadeValue}%, #ef4444 ${100-state.player.crossfadeValue}%, #ef4444 100%)`
                            }}
                          />
                          <div className="absolute top-4 left-0 right-0 flex justify-between text-xs text-gray-400">
                            <span>DECK A</span>
                            <span>DECK B</span>
                          </div>
                        </div>
                        <span className="text-lg font-bold text-red-400 w-8 text-center">B</span>
                      </div>

                      <div className="flex justify-between text-sm font-mono bg-gray-700 rounded-lg p-3 mb-4">
                        <span className="text-blue-300">A: {100 - state.player.crossfadeValue}%</span>
                        <span className="text-red-300">B: {state.player.crossfadeValue}%</span>
                      </div>

                      <div className="pt-4 border-t border-gray-600">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-200">Master Volume</span>
                          <span className="text-lg font-mono font-bold text-yellow-400">{state.player.masterVolume}%</span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={state.player.masterVolume}
                          onChange={(e) => {
                            const newValue = parseInt(e.target.value);
                            dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: { masterVolume: newValue }
                            });
                            effectEngine.updateCrossfade(state.player.crossfadeValue, newValue);
                          }}
                          className="w-full h-3 bg-gray-600 rounded-lg appearance-none cursor-pointer master-volume"
                        />
                      </div>

                      <div className="mt-6 grid grid-cols-2 gap-3">
                        <button
                          onClick={() => {
                            effectEngine.stopAllAnimations();
                            dispatch({
                              type: 'UPDATE_PLAYER',
                              payload: {
                                deckA: { ...state.player.deckA, isPlaying: false },
                                deckB: { ...state.player.deckB, isPlaying: false }
                              }
                            });
                          }}
                          className="py-3 text-sm font-bold bg-red-600 hover:bg-red-700 rounded-lg transition-all transform hover:scale-105 shadow-lg shadow-red-500/50"
                        >
                          🛑 STOP ALL
                        </button>

                        <button
                          onClick={() => beatEngine.tapBeat()}
                          className="py-3 text-sm font-bold bg-blue-600 hover:bg-blue-700 rounded-lg transition-all transform hover:scale-105 shadow-lg shadow-blue-500/50"
                        >
                          🎵 TAP: {state.beatEngine.bpm}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </aside>





          {/* Center - SVG Canvas */}
          <main className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
            <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between flex-shrink-0">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Canvas:</span>
                <span className="text-sm font-mono">{state.project.svgContent ? 'SVG Loaded' : 'No SVG'}</span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    const container = document.querySelector('.svg-viewport');
                    if (container) {
                      const currentZoom = parseFloat(container.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || '1');
                      const newZoom = Math.min(currentZoom * 1.2, 5);
                      container.style.transform = `scale(${newZoom})`;
                    }
                  }}
                  className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                >
                  Zoom +
                </button>
                <button
                  onClick={() => {
                    const container = document.querySelector('.svg-viewport');
                    if (container) {
                      const currentZoom = parseFloat(container.style.transform?.match(/scale\(([^)]+)\)/)?.[1] || '1');
                      const newZoom = Math.max(currentZoom / 1.2, 0.1);
                      container.style.transform = `scale(${newZoom})`;
                    }
                  }}
                  className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                >
                  Zoom -
                </button>
                <button
                  onClick={() => {
                    const container = document.querySelector('.svg-viewport');
                    if (container) {
                      container.style.transform = 'scale(1)';
                      const scrollContainer = document.querySelector('.svg-scroll-container');
                      if (scrollContainer) {
                        scrollContainer.scrollTop = 0;
                        scrollContainer.scrollLeft = 0;
                      }
                    }
                  }}
                  className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs"
                >
                  Reset
                </button>
              </div>
            </div>

            <div className="flex-1 bg-gray-900 overflow-auto svg-scroll-container">
              <div className="min-h-full min-w-full flex items-center justify-center p-8">
                <div className="svg-viewport" style={{ transform: 'scale(1)', transformOrigin: 'center' }}>
                  <div ref={svgRef} className="svg-container" style={{ minHeight: '400px', minWidth: '600px' }} />
                </div>
              </div>
            </div>

            <div className="h-24 bg-gray-800 border-t border-gray-700 p-4">
              <div className="text-xs text-gray-500 mb-2">Timeline (Coming Soon)</div>
              <div className="h-12 bg-gray-700 rounded-lg"></div>
            </div>
          </main>

          {/* Right Sidebar - Sequence Management & Effects */}
          <aside className={`bg-gray-800 border-l border-gray-700 transition-all flex flex-col ${
            state.ui.rightSidebarCollapsed ? 'w-12' : 'w-80'
          }`}>
            <div className="p-4 border-b border-gray-700 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h2 className={`font-semibold ${state.ui.rightSidebarCollapsed ? 'hidden' : ''}`}>
                  Sequence Management
                </h2>
                <button
                  onClick={() => dispatch({
                    type: 'UPDATE_UI',
                    payload: { rightSidebarCollapsed: !state.ui.rightSidebarCollapsed }
                  })}
                  className="p-1 hover:bg-gray-700 rounded"
                >
                  {state.ui.rightSidebarCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
                </button>
              </div>
            </div>

            {!state.ui.rightSidebarCollapsed && (
              <div className="flex-1 overflow-y-auto">
                <div className="p-4 space-y-4">

                {/* Sequences Accordion */}
                <div className="bg-gray-700 rounded-lg overflow-hidden">
                  <button
                    onClick={() => dispatch({
                      type: 'UPDATE_UI',
                      payload: { sequencesExpanded: !state.ui.sequencesExpanded }
                    })}
                    className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-600 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Sequenzen</span>
                      <span className="text-xs bg-blue-600 px-2 py-1 rounded-full">
                        {state.project.sequences.length}
                      </span>
                    </div>
                    <ChevronDown
                      size={16}
                      className={`transform transition-transform ${
                        state.ui.sequencesExpanded ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {state.ui.sequencesExpanded && (
                    <div className="p-4 bg-gray-750 border-t border-gray-600 space-y-3">
                      <button
                        onClick={createSequence}
                        className="w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg flex items-center justify-center gap-2 transition-colors"
                      >
                        <Plus size={16} />
                        <span className="text-sm">Neue Sequenz</span>
                      </button>

                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {state.project.sequences.map(sequence => (
                          <div
                            key={sequence.id}
                            className={`rounded-lg transition-all ${
                              state.player.currentSequence?.id === sequence.id
                                ? 'bg-blue-600 shadow-lg'
                                : 'bg-gray-700'
                            }`}
                          >
                            <div
                              onClick={() => dispatch({
                                type: 'UPDATE_PLAYER',
                                payload: { currentSequence: sequence }
                              })}
                              className="p-3 cursor-pointer hover:bg-opacity-80"
                            >
                              <div className="flex items-center justify-between mb-1">
                                <span className="font-medium text-sm">{sequence.name}</span>
                                <div className="flex items-center gap-1">
                                  {sequence.loop && <Repeat size={12} />}
                                  {sequence.bpmSync && <Activity size={12} />}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleSequenceExpansion(sequence.id);
                                    }}
                                    className="p-1 hover:bg-gray-600 rounded"
                                  >
                                    {expandedSequences.has(sequence.id) ?
                                      <ChevronLeft size={12} /> :
                                      <ChevronRight size={12} />
                                    }
                                  </button>
                                </div>
                              </div>
                              <div className="text-xs text-gray-400 mb-2">
                                {sequence.duration}ms • {sequence.elements.length} Elemente
                              </div>
                              <div className="text-xs text-gray-300">
                                Effekte: {sequence.elements.flatMap(el => el.effects.map(eff => eff.type)).join(', ')}
                              </div>
                              <div className="mt-2 flex gap-1">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    dispatch({
                                      type: 'UPDATE_PLAYER',
                                      payload: { currentSequence: sequence }
                                    });
                                    playCurrentSequence();
                                  }}
                                  className="p-1 bg-green-600 hover:bg-green-700 rounded"
                                >
                                  <Play size={12} />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    editSequence(sequence);
                                  }}
                                  className="p-1 bg-gray-600 hover:bg-gray-500 rounded"
                                >
                                  <Edit2 size={12} />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    copySequence(sequence);
                                  }}
                                  className="p-1 bg-gray-600 hover:bg-gray-500 rounded"
                                >
                                  <Copy size={12} />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    deleteSequence(sequence.id);
                                  }}
                                  className="p-1 bg-red-600 hover:bg-red-700 rounded"
                                >
                                  <Trash2 size={12} />
                                </button>
                              </div>
                            </div>

                            {expandedSequences.has(sequence.id) && (
                              <div className="px-3 pb-3 border-t border-gray-600">
                                <div className="text-xs font-medium text-gray-300 mt-2 mb-2">Effekte:</div>
                                {sequence.elements.length === 0 ? (
                                  <div className="text-xs text-gray-500 italic">Keine Effekte</div>
                                ) : (
                                  sequence.elements.map(element => (
                                    <div key={element.id} className="mb-2">
                                      <div className="text-xs font-medium text-gray-400 mb-1">
                                        Element: {element.id}
                                      </div>
                                      {element.effects.map((effect, effectIndex) => (
                                        <div
                                          key={effectIndex}
                                          className="flex items-center justify-between bg-gray-800 rounded px-2 py-1 mb-1"
                                        >
                                          <span className="text-xs text-gray-300">
                                            {effect.type}
                                            {effect.color && ` (${effect.color})`}
                                            {effect.from && effect.to && ` (${effect.from} → ${effect.to})`}
                                          </span>
                                          <button
                                            onClick={() => removeEffectFromSequence(sequence.id, element.id, effectIndex)}
                                            className="p-1 text-red-400 hover:text-red-300 hover:bg-red-900 rounded"
                                          >
                                            <X size={10} />
                                          </button>
                                        </div>
                                      ))}
                                    </div>
                                  ))
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Effects Accordion Structure */}
                <EffectsAccordion
                  selectedElement={state.editor.selectedElement}
                  currentSequence={state.player.currentSequence}
                  groupEngine={groupEngine}
                  effectEngine={effectEngine}
                  onAddEffect={addEffectToSequence}
                  onAddGroupEffectToSequence={addGroupEffectToSequence}
                />
                </div>
              </div>
            )}
          </aside>
        </div>

        <NotificationSystem />
        <PerformanceMonitor />
      </div>
    </AppContext.Provider>
  );
};

export default FachwerkwandApp;